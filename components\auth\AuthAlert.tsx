'use client';

import { motion, AnimatePresence } from 'framer-motion';
import { AlertCircle, CheckCircle, Info, X } from 'lucide-react';
import { cn } from '@/lib/utils';

interface AuthAlertProps {
  type?: 'error' | 'success' | 'info' | 'warning';
  title?: string;
  message: string;
  show: boolean;
  onClose?: () => void;
  dismissible?: boolean;
}

export default function AuthAlert({
  type = 'error',
  title,
  message,
  show,
  onClose,
  dismissible = true
}: AuthAlertProps) {
  const icons = {
    error: AlertCircle,
    success: CheckCircle,
    info: Info,
    warning: AlertCircle
  };

  const styles = {
    error: "bg-red-900/20 border-red-500/30 text-red-300",
    success: "bg-green-900/20 border-green-500/30 text-green-300",
    info: "bg-blue-900/20 border-blue-500/30 text-blue-300",
    warning: "bg-yellow-900/20 border-yellow-500/30 text-yellow-300"
  };

  const iconStyles = {
    error: "text-red-400",
    success: "text-green-400",
    info: "text-blue-400",
    warning: "text-yellow-400"
  };

  const Icon = icons[type];

  return (
    <AnimatePresence>
      {show && (
        <motion.div
          initial={{ opacity: 0, y: -10, scale: 0.95 }}
          animate={{ opacity: 1, y: 0, scale: 1 }}
          exit={{ opacity: 0, y: -10, scale: 0.95 }}
          transition={{ duration: 0.2 }}
          className={cn(
            "relative p-4 rounded-lg border backdrop-blur-sm",
            styles[type]
          )}
        >
          <div className="flex items-start">
            <Icon className={cn("h-5 w-5 mt-0.5 mr-3 flex-shrink-0", iconStyles[type])} />
            
            <div className="flex-1 min-w-0">
              {title && (
                <h4 className="font-medium mb-1">{title}</h4>
              )}
              <p className="text-sm opacity-90">{message}</p>
            </div>
            
            {dismissible && onClose && (
              <button
                onClick={onClose}
                className="ml-3 flex-shrink-0 opacity-70 hover:opacity-100 transition-opacity"
              >
                <X className="h-4 w-4" />
              </button>
            )}
          </div>
        </motion.div>
      )}
    </AnimatePresence>
  );
}
