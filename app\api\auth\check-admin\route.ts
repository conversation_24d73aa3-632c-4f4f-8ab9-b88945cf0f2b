import { NextRequest, NextResponse } from 'next/server';
import { createSupabaseServerClient } from '@/lib/supabase/client';

export async function GET(request: NextRequest) {
  try {
    const serverClient = createSupabaseServerClient();
    
    // Get the current session
    const { data: { session }, error: sessionError } = await serverClient.auth.getSession();
    
    if (sessionError || !session) {
      return NextResponse.json({ isAdmin: false, error: 'Not authenticated' }, { status: 401 });
    }

    // Get user details from our users table
    const { data: user, error: userError } = await serverClient
      .from('users')
      .select('role, is_active')
      .eq('id', session.user.id)
      .single();

    if (userError || !user) {
      return NextResponse.json({ isAdmin: false, error: 'User not found' }, { status: 404 });
    }

    const isAdmin = user.role === 'ADMIN' && user.is_active;

    if (isAdmin) {
      // Update last login time
      await serverClient
        .from('users')
        .update({ last_login: new Date().toISOString() })
        .eq('id', session.user.id);
    }

    return NextResponse.json({ 
      isAdmin,
      user: {
        id: session.user.id,
        email: session.user.email,
        role: user.role,
        is_active: user.is_active
      }
    });

  } catch (error: any) {
    console.error('Admin check error:', error);
    return NextResponse.json(
      { isAdmin: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}
