'use client';

import { useState, useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
import { motion } from 'framer-motion';
import Link from 'next/link';
import { Mail, Lock, LogIn } from 'lucide-react';

import AuthLayout from '@/components/auth/AuthLayout';
import AuthInput from '@/components/auth/AuthInput';
import AuthButton from '@/components/auth/AuthButton';
import AuthAlert from '@/components/auth/AuthAlert';
import SocialAuth from '@/components/auth/SocialAuth';

export default function CoursesLoginPage() {
  const [formData, setFormData] = useState({
    email: '',
    password: '',
    rememberMe: false
  });
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  
  const router = useRouter();
  const searchParams = useSearchParams();
  const { signIn, user } = useAuth();
  
  const redirectTo = searchParams.get('redirect') || '/courses/dashboard';

  // Redirect if already logged in
  useEffect(() => {
    if (user) {
      router.push(redirectTo);
    }
  }, [user, router, redirectTo]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value, type, checked } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));
    
    // Clear error when user starts typing
    if (error) setError('');
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError('');

    // Basic validation
    if (!formData.email || !formData.password) {
      setError('Please fill in all fields');
      setIsLoading(false);
      return;
    }

    try {
      const result = await signIn(formData.email, formData.password);

      if (result.success) {
        setSuccess('Login successful! Redirecting...');
        
        // Set remember me cookie if checked
        if (formData.rememberMe) {
          document.cookie = 'innohub_remember=true; max-age=2592000; path=/'; // 30 days
        }
        
        // Redirect after a brief delay
        setTimeout(() => {
          router.push(redirectTo);
        }, 1000);
      } else {
        setError(result.error || 'Invalid email or password');
      }
    } catch (error: any) {
      console.error('Login error:', error);
      setError(error.message || 'An unexpected error occurred');
    } finally {
      setIsLoading(false);
    }
  };

  const handleSocialSuccess = () => {
    setSuccess('Login successful! Redirecting...');
    setTimeout(() => {
      router.push(redirectTo);
    }, 1000);
  };

  const handleSocialError = (error: string) => {
    setError(error);
  };

  return (
    <AuthLayout
      title="Welcome Back"
      subtitle="Sign in to access your courses and continue learning"
      backHref="/courses/welcome"
      backText="Back to Courses"
    >
      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Alert Messages */}
        <AuthAlert
          type="error"
          message={error}
          show={!!error}
          onClose={() => setError('')}
        />
        
        <AuthAlert
          type="success"
          message={success}
          show={!!success}
          dismissible={false}
        />

        {/* Email Input */}
        <AuthInput
          label="Email Address"
          name="email"
          type="email"
          icon={Mail}
          placeholder="Enter your email"
          value={formData.email}
          onChange={handleInputChange}
          required
          autoComplete="email"
        />

        {/* Password Input */}
        <AuthInput
          label="Password"
          name="password"
          type="password"
          icon={Lock}
          placeholder="Enter your password"
          value={formData.password}
          onChange={handleInputChange}
          showPasswordToggle
          required
          autoComplete="current-password"
        />

        {/* Remember Me & Forgot Password */}
        <div className="flex items-center justify-between">
          <label className="flex items-center">
            <input
              type="checkbox"
              name="rememberMe"
              checked={formData.rememberMe}
              onChange={handleInputChange}
              className="w-4 h-4 text-purple-600 bg-gray-900 border-gray-600 rounded focus:ring-purple-500 focus:ring-2"
            />
            <span className="ml-2 text-sm text-gray-400">Remember me</span>
          </label>
          
          <Link
            href="/courses/auth/forgot-password"
            className="text-sm text-purple-400 hover:text-purple-300 transition-colors"
          >
            Forgot password?
          </Link>
        </div>

        {/* Submit Button */}
        <AuthButton
          type="submit"
          loading={isLoading}
          icon={LogIn}
          className="w-full"
          disabled={!formData.email || !formData.password}
        >
          {isLoading ? 'Signing In...' : 'Sign In'}
        </AuthButton>

        {/* Social Authentication */}
        <SocialAuth
          mode="signin"
          onSuccess={handleSocialSuccess}
          onError={handleSocialError}
        />

        {/* Sign Up Link */}
        <div className="text-center">
          <p className="text-gray-400">
            New to InnoHub?{' '}
            <Link
              href="/courses/auth/register"
              className="text-purple-400 hover:text-purple-300 font-medium transition-colors"
            >
              Create an account
            </Link>
          </p>
        </div>
      </form>
    </AuthLayout>
  );
}
