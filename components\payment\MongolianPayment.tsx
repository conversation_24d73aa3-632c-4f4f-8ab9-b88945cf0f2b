'use client';

import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import {
  Loader2,
  Smartphone,
  QrCode,
  CheckCircle,
  Shield,
  Clock,
  ArrowRight,
  RefreshCw,
  AlertCircle,
  Zap
} from 'lucide-react';
import Image from 'next/image';
import { useAuth } from '@/contexts/AuthContext';

interface QPayPaymentProps {
  courseId: string;
  courseName: string;
  amount: number;
  currency?: string;
  onSuccess?: (paymentData: any) => void;
  onCancel?: () => void;
}

type PaymentStep = 'info' | 'processing' | 'qr' | 'checking' | 'success' | 'error';

export default function QPayPayment({
  courseId,
  courseName,
  amount,
  currency = 'MNT',
  onSuccess,
  onCancel
}: QPayPaymentProps) {
  const [currentStep, setCurrentStep] = useState<PaymentStep>('info');
  const [error, setError] = useState('');
  const [paymentData, setPaymentData] = useState<any>(null);
  const [progress, setProgress] = useState(0);
  const [timeLeft, setTimeLeft] = useState(300); // 5 minutes
  const { user } = useAuth();

  // Timer effect for QR code expiration
  useEffect(() => {
    let interval: NodeJS.Timeout;
    if (currentStep === 'qr' && timeLeft > 0) {
      interval = setInterval(() => {
        setTimeLeft((prev) => prev - 1);
      }, 1000);
    }
    return () => clearInterval(interval);
  }, [currentStep, timeLeft]);

  // Progress animation
  useEffect(() => {
    if (currentStep === 'processing') {
      const timer = setInterval(() => {
        setProgress((prev) => {
          if (prev >= 100) {
            clearInterval(timer);
            return 100;
          }
          return prev + 10;
        });
      }, 200);
      return () => clearInterval(timer);
    }
  }, [currentStep]);

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('mn-MN', {
      style: 'currency',
      currency: 'MNT',
      minimumFractionDigits: 0
    }).format(price);
  };

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const handlePayment = async () => {
    if (!user) {
      setError('Нэвтэрч орно уу');
      return;
    }

    setCurrentStep('processing');
    setError('');
    setProgress(0);

    try {
      const response = await fetch('/api/payment/create', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          courseId,
          amount,
          currency,
          userId: user.id
        })
      });

      const result = await response.json();

      if (result.success) {
        setPaymentData(result);
        setCurrentStep('qr');
        setTimeLeft(300); // Reset timer
      } else {
        setError(result.error || 'Төлбөр үүсгэхэд алдаа гарлаа');
        setCurrentStep('error');
      }
    } catch (error: any) {
      console.error('Payment error:', error);
      setError('Төлбөр үүсгэхэд алдаа гарлаа');
      setCurrentStep('error');
    }
  };

  const checkPaymentStatus = async () => {
    if (!paymentData?.transactionId) return;

    setCurrentStep('checking');

    try {
      const response = await fetch(
        `/api/payment/create?provider=qpay&transactionId=${paymentData.transactionId}`
      );
      const status = await response.json();

      if (status.status === 'completed') {
        setCurrentStep('success');
        setTimeout(() => {
          onSuccess?.(paymentData);
        }, 2000);
      } else {
        setCurrentStep('qr');
      }
    } catch (error) {
      console.error('Status check error:', error);
      setCurrentStep('qr');
    }
  };

  const handleRetry = () => {
    setCurrentStep('info');
    setError('');
    setProgress(0);
    setPaymentData(null);
  };

  // Step indicator component
  const StepIndicator = ({ step, isActive, isCompleted }: { step: string, isActive: boolean, isCompleted: boolean }) => (
    <div className={`flex items-center gap-2 ${isActive ? 'text-purple-400' : isCompleted ? 'text-green-400' : 'text-gray-500'}`}>
      <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
        isActive ? 'bg-purple-500/20 border-2 border-purple-500' :
        isCompleted ? 'bg-green-500/20 border-2 border-green-500' :
        'bg-gray-700 border-2 border-gray-600'
      }`}>
        {isCompleted ? <CheckCircle className="h-4 w-4" /> : step}
      </div>
    </div>
  );

  return (
    <div className="w-full max-w-md mx-auto">
      <AnimatePresence mode="wait">
        {/* Step 1: Payment Info */}
        {currentStep === 'info' && (
          <motion.div
            key="info"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            transition={{ duration: 0.3 }}
          >
            <Card className="border-purple-500/20 bg-black/60 backdrop-blur-md overflow-hidden">
              <CardHeader className="bg-gradient-to-r from-purple-600/20 to-blue-600/20 border-b border-purple-500/20">
                <div className="flex items-center justify-between">
                  <CardTitle className="text-white flex items-center gap-2">
                    <Smartphone className="h-6 w-6 text-purple-400" />
                    QPay төлбөр
                  </CardTitle>
                  <Badge className="bg-green-500/20 text-green-400 border-green-500/30">
                    Найдвартай
                  </Badge>
                </div>
              </CardHeader>

              <CardContent className="p-6 space-y-6">
                {/* Course Info */}
                <div className="bg-gradient-to-r from-purple-500/10 to-blue-500/10 border border-purple-500/20 rounded-xl p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <h3 className="text-white font-medium text-lg">{courseName}</h3>
                      <p className="text-gray-400 text-sm mt-1">InnoHub хичээл</p>
                    </div>
                    <div className="text-right">
                      <p className="text-purple-400 text-2xl font-bold">
                        {formatPrice(amount)}
                      </p>
                      <p className="text-gray-400 text-xs">MNT</p>
                    </div>
                  </div>
                </div>

                {error && (
                  <Alert className="border-red-500/20 bg-red-500/10">
                    <AlertCircle className="h-4 w-4" />
                    <AlertDescription className="text-red-400">
                      {error}
                    </AlertDescription>
                  </Alert>
                )}

                {/* QPay Features */}
                <div className="space-y-4">
                  <h4 className="text-white font-medium flex items-center gap-2">
                    <Zap className="h-4 w-4 text-yellow-400" />
                    QPay давуу тал
                  </h4>

                  <div className="grid grid-cols-2 gap-3">
                    <div className="bg-gray-800/50 rounded-lg p-3 text-center">
                      <Shield className="h-6 w-6 text-green-400 mx-auto mb-2" />
                      <p className="text-white text-sm font-medium">Аюулгүй</p>
                      <p className="text-gray-400 text-xs">256-bit шифрлэлт</p>
                    </div>
                    <div className="bg-gray-800/50 rounded-lg p-3 text-center">
                      <Zap className="h-6 w-6 text-yellow-400 mx-auto mb-2" />
                      <p className="text-white text-sm font-medium">Хурдан</p>
                      <p className="text-gray-400 text-xs">Шуурхай гүйлгээ</p>
                    </div>
                  </div>
                </div>

                {/* Payment Button */}
                <Button
                  onClick={handlePayment}
                  className="w-full bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white font-medium py-3 text-lg rounded-xl"
                >
                  <Smartphone className="h-5 w-5 mr-2" />
                  QPay-ээр төлөх
                  <ArrowRight className="h-5 w-5 ml-2" />
                </Button>

                {onCancel && (
                  <Button
                    onClick={onCancel}
                    variant="outline"
                    className="w-full border-gray-600 text-gray-300 hover:bg-gray-800 rounded-xl"
                  >
                    Цуцлах
                  </Button>
                )}

                {/* Security Notice */}
                <div className="text-center text-xs text-gray-500 bg-gray-800/30 rounded-lg p-3">
                  <Shield className="h-4 w-4 inline mr-1" />
                  SSL шифрлэлттэй найдвартай төлбөр
                </div>
              </CardContent>
            </Card>
          </motion.div>
        )}

        {/* Step 2: Processing */}
        {currentStep === 'processing' && (
          <motion.div
            key="processing"
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.9 }}
            transition={{ duration: 0.3 }}
          >
            <Card className="border-purple-500/20 bg-black/60 backdrop-blur-md">
              <CardContent className="p-8 text-center space-y-6">
                <div className="relative">
                  <div className="w-20 h-20 bg-purple-500/20 rounded-full flex items-center justify-center mx-auto">
                    <Loader2 className="h-10 w-10 text-purple-400 animate-spin" />
                  </div>
                  <div className="absolute inset-0 rounded-full border-2 border-purple-500/20 animate-pulse"></div>
                </div>

                <div>
                  <h3 className="text-white text-xl font-medium mb-2">Төлбөр үүсгэж байна...</h3>
                  <p className="text-gray-400">QPay серверт холбогдож байна</p>
                </div>

                <div className="space-y-2">
                  <Progress value={progress} className="w-full" />
                  <p className="text-gray-400 text-sm">{progress}% дууссан</p>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        )}

        {/* Step 3: QR Code */}
        {currentStep === 'qr' && paymentData && (
          <motion.div
            key="qr"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            transition={{ duration: 0.3 }}
          >
            <Card className="border-purple-500/20 bg-black/60 backdrop-blur-md">
              <CardHeader className="text-center bg-gradient-to-r from-purple-600/20 to-blue-600/20 border-b border-purple-500/20">
                <CardTitle className="text-white flex items-center justify-center gap-2">
                  <QrCode className="h-6 w-6" />
                  QPay QR код
                </CardTitle>
                <div className="flex items-center justify-center gap-2 text-sm">
                  <Clock className="h-4 w-4 text-yellow-400" />
                  <span className="text-yellow-400 font-mono">{formatTime(timeLeft)}</span>
                  <span className="text-gray-400">үлдсэн</span>
                </div>
              </CardHeader>

              <CardContent className="p-6 space-y-6">
                <div className="text-center">
                  <div className="bg-white p-6 rounded-2xl inline-block shadow-2xl">
                    <Image
                      src={paymentData.qrCode || '/images/qr-placeholder.svg'}
                      alt="QPay QR Code"
                      width={200}
                      height={200}
                      className="mx-auto"
                    />
                  </div>

                  <div className="mt-4 space-y-2">
                    <p className="text-white font-medium">QPay аппаар QR код уншуулна уу</p>
                    <p className="text-gray-400 text-sm">
                      Утасны камераар QR кодыг уншуулж төлбөр хийнэ үү
                    </p>
                  </div>
                </div>

                {/* Payment Amount */}
                <div className="bg-purple-500/10 border border-purple-500/20 rounded-xl p-4 text-center">
                  <p className="text-gray-400 text-sm">Төлөх дүн</p>
                  <p className="text-purple-400 text-2xl font-bold">{formatPrice(amount)}</p>
                </div>

                {/* Action Buttons */}
                <div className="space-y-3">
                  <Button
                    onClick={checkPaymentStatus}
                    className="w-full bg-green-600 hover:bg-green-700 text-white rounded-xl"
                  >
                    <CheckCircle className="h-4 w-4 mr-2" />
                    Төлбөр шалгах
                  </Button>

                  <Button
                    onClick={() => setCurrentStep('info')}
                    variant="outline"
                    className="w-full border-gray-600 text-gray-300 hover:bg-gray-800 rounded-xl"
                  >
                    Буцах
                  </Button>
                </div>

                {/* Instructions */}
                <div className="bg-blue-500/10 border border-blue-500/20 rounded-xl p-4">
                  <h4 className="text-blue-400 font-medium mb-2">Хэрхэн төлөх вэ?</h4>
                  <ol className="text-gray-400 text-sm space-y-1">
                    <li>1. QPay аппыг нээнэ үү</li>
                    <li>2. QR код уншуулах товчийг дарна уу</li>
                    <li>3. Дээрх QR кодыг уншуулна уу</li>
                    <li>4. Төлбөрийн мэдээллийг баталгаажуулна уу</li>
                  </ol>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        )}

        {/* Step 4: Checking Payment */}
        {currentStep === 'checking' && (
          <motion.div
            key="checking"
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.9 }}
            transition={{ duration: 0.3 }}
          >
            <Card className="border-yellow-500/20 bg-black/60 backdrop-blur-md">
              <CardContent className="p-8 text-center space-y-6">
                <div className="relative">
                  <div className="w-20 h-20 bg-yellow-500/20 rounded-full flex items-center justify-center mx-auto">
                    <RefreshCw className="h-10 w-10 text-yellow-400 animate-spin" />
                  </div>
                </div>

                <div>
                  <h3 className="text-white text-xl font-medium mb-2">Төлбөр шалгаж байна...</h3>
                  <p className="text-gray-400">QPay серверээс төлбөрийн мэдээлэл авч байна</p>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        )}

        {/* Step 5: Success */}
        {currentStep === 'success' && (
          <motion.div
            key="success"
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.8 }}
            transition={{ duration: 0.5, type: "spring" }}
          >
            <Card className="border-green-500/20 bg-black/60 backdrop-blur-md">
              <CardContent className="p-8 text-center space-y-6">
                <motion.div
                  initial={{ scale: 0 }}
                  animate={{ scale: 1 }}
                  transition={{ delay: 0.2, type: "spring", stiffness: 200 }}
                  className="relative"
                >
                  <div className="w-20 h-20 bg-green-500/20 rounded-full flex items-center justify-center mx-auto">
                    <CheckCircle className="h-10 w-10 text-green-400" />
                  </div>
                  <div className="absolute inset-0 rounded-full border-2 border-green-500/20 animate-ping"></div>
                </motion.div>

                <div>
                  <h3 className="text-white text-2xl font-bold mb-2">Амжилттай!</h3>
                  <p className="text-gray-400">Таны төлбөр амжилттай хийгдлээ</p>
                </div>

                <div className="bg-green-500/10 border border-green-500/20 rounded-xl p-4">
                  <p className="text-green-400 font-medium">{formatPrice(amount)} төлөгдлөө</p>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        )}

        {/* Step 6: Error */}
        {currentStep === 'error' && (
          <motion.div
            key="error"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            transition={{ duration: 0.3 }}
          >
            <Card className="border-red-500/20 bg-black/60 backdrop-blur-md">
              <CardContent className="p-8 text-center space-y-6">
                <div className="w-20 h-20 bg-red-500/20 rounded-full flex items-center justify-center mx-auto">
                  <AlertCircle className="h-10 w-10 text-red-400" />
                </div>

                <div>
                  <h3 className="text-white text-xl font-medium mb-2">Алдаа гарлаа</h3>
                  <p className="text-gray-400">{error}</p>
                </div>

                <Button
                  onClick={handleRetry}
                  className="w-full bg-purple-600 hover:bg-purple-700 text-white rounded-xl"
                >
                  <RefreshCw className="h-4 w-4 mr-2" />
                  Дахин оролдох
                </Button>
              </CardContent>
            </Card>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
}
