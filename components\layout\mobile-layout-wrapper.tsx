'use client';

import { ReactNode, useEffect, useState } from 'react';
import { useMobile, useMobileViewport } from '@/hooks/use-mobile';
import { cn } from '@/lib/utils';

interface MobileLayoutWrapperProps {
  children: ReactNode;
  className?: string;
  enableSafeArea?: boolean;
  enableKeyboardAdjustment?: boolean;
}

export function MobileLayoutWrapper({
  children,
  className,
  enableSafeArea = true,
  enableKeyboardAdjustment = true
}: MobileLayoutWrapperProps) {
  const { isMobile, isTablet, orientation } = useMobile();
  const { viewportHeight, isKeyboardOpen, cssVh } = useMobileViewport();
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  useEffect(() => {
    if (mounted && typeof window !== 'undefined') {
      // Set CSS custom properties for mobile viewport
      document.documentElement.style.setProperty('--vh', cssVh);
      document.documentElement.style.setProperty('--viewport-height', `${viewportHeight}px`);
      
      // Add mobile-specific classes to body
      const body = document.body;
      body.classList.toggle('mobile', isMobile);
      body.classList.toggle('tablet', isTablet);
      body.classList.toggle('landscape', orientation === 'landscape');
      body.classList.toggle('portrait', orientation === 'portrait');
      body.classList.toggle('keyboard-open', isKeyboardOpen);
      
      // Prevent zoom on input focus (iOS)
      if (isMobile) {
        const viewport = document.querySelector('meta[name="viewport"]');
        if (viewport) {
          viewport.setAttribute('content', 
            'width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no'
          );
        }
      }
    }
  }, [mounted, isMobile, isTablet, orientation, isKeyboardOpen, viewportHeight, cssVh]);

  if (!mounted) {
    // Prevent hydration mismatch
    return <div className={className}>{children}</div>;
  }

  return (
    <div
      className={cn(
        'min-h-screen w-full',
        // Safe area support
        enableSafeArea && [
          'supports-[padding:max(0px)]:pt-[max(env(safe-area-inset-top),0px)]',
          'supports-[padding:max(0px)]:pb-[max(env(safe-area-inset-bottom),0px)]',
          'supports-[padding:max(0px)]:pl-[max(env(safe-area-inset-left),0px)]',
          'supports-[padding:max(0px)]:pr-[max(env(safe-area-inset-right),0px)]'
        ],
        // Keyboard adjustment
        enableKeyboardAdjustment && isKeyboardOpen && 'pb-0',
        // Orientation-specific styles
        orientation === 'landscape' && isMobile && 'landscape:min-h-[100dvh]',
        className
      )}
      style={{
        minHeight: enableKeyboardAdjustment ? `${viewportHeight}px` : '100vh'
      }}
    >
      {children}
    </div>
  );
}

// Mobile-specific breakpoint component
interface MobileBreakpointProps {
  children: ReactNode;
  show?: 'mobile' | 'tablet' | 'desktop' | 'mobile-tablet' | 'tablet-desktop';
  hide?: 'mobile' | 'tablet' | 'desktop' | 'mobile-tablet' | 'tablet-desktop';
}

export function MobileBreakpoint({ children, show, hide }: MobileBreakpointProps) {
  const { isMobile, isTablet, isDesktop } = useMobile();
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  if (!mounted) return null;

  // Determine if component should be shown
  let shouldShow = true;

  if (show) {
    switch (show) {
      case 'mobile':
        shouldShow = isMobile;
        break;
      case 'tablet':
        shouldShow = isTablet;
        break;
      case 'desktop':
        shouldShow = isDesktop;
        break;
      case 'mobile-tablet':
        shouldShow = isMobile || isTablet;
        break;
      case 'tablet-desktop':
        shouldShow = isTablet || isDesktop;
        break;
    }
  }

  if (hide) {
    switch (hide) {
      case 'mobile':
        shouldShow = shouldShow && !isMobile;
        break;
      case 'tablet':
        shouldShow = shouldShow && !isTablet;
        break;
      case 'desktop':
        shouldShow = shouldShow && !isDesktop;
        break;
      case 'mobile-tablet':
        shouldShow = shouldShow && !(isMobile || isTablet);
        break;
      case 'tablet-desktop':
        shouldShow = shouldShow && !(isTablet || isDesktop);
        break;
    }
  }

  return shouldShow ? <>{children}</> : null;
}

// Mobile-optimized container with automatic responsive behavior
interface ResponsiveContainerProps {
  children: ReactNode;
  className?: string;
  maxWidth?: 'sm' | 'md' | 'lg' | 'xl' | '2xl' | 'full';
  padding?: 'none' | 'sm' | 'md' | 'lg';
  center?: boolean;
}

const maxWidths = {
  sm: 'max-w-sm',
  md: 'max-w-md',
  lg: 'max-w-4xl',
  xl: 'max-w-6xl',
  '2xl': 'max-w-7xl',
  full: 'max-w-full'
};

const paddings = {
  none: '',
  sm: 'px-4 sm:px-6',
  md: 'px-4 sm:px-6 md:px-8',
  lg: 'px-4 sm:px-6 md:px-8 lg:px-12'
};

export function ResponsiveContainer({
  children,
  className,
  maxWidth = 'xl',
  padding = 'md',
  center = true
}: ResponsiveContainerProps) {
  return (
    <div
      className={cn(
        'w-full',
        maxWidths[maxWidth],
        paddings[padding],
        center && 'mx-auto',
        className
      )}
    >
      {children}
    </div>
  );
}

// Mobile-optimized grid system
interface MobileGridSystemProps {
  children: ReactNode;
  className?: string;
  cols?: {
    mobile?: number;
    tablet?: number;
    desktop?: number;
  };
  gap?: 'sm' | 'md' | 'lg' | 'xl';
  align?: 'start' | 'center' | 'end' | 'stretch';
}

const gaps = {
  sm: 'gap-2 sm:gap-3',
  md: 'gap-4 sm:gap-6',
  lg: 'gap-6 sm:gap-8',
  xl: 'gap-8 sm:gap-12'
};

const alignments = {
  start: 'items-start',
  center: 'items-center',
  end: 'items-end',
  stretch: 'items-stretch'
};

export function MobileGridSystem({
  children,
  className,
  cols = { mobile: 1, tablet: 2, desktop: 3 },
  gap = 'md',
  align = 'stretch'
}: MobileGridSystemProps) {
  const gridCols = [
    cols.mobile && `grid-cols-${cols.mobile}`,
    cols.tablet && `sm:grid-cols-${cols.tablet}`,
    cols.desktop && `lg:grid-cols-${cols.desktop}`
  ].filter(Boolean).join(' ');

  return (
    <div
      className={cn(
        'grid',
        gridCols,
        gaps[gap],
        alignments[align],
        className
      )}
    >
      {children}
    </div>
  );
}

// Mobile-optimized section wrapper
interface MobileSectionProps {
  children: ReactNode;
  className?: string;
  spacing?: 'sm' | 'md' | 'lg' | 'xl';
  background?: 'transparent' | 'dark' | 'gradient';
}

const spacings = {
  sm: 'py-8 sm:py-12',
  md: 'py-12 sm:py-16 md:py-20',
  lg: 'py-16 sm:py-20 md:py-24 lg:py-32',
  xl: 'py-20 sm:py-24 md:py-32 lg:py-40'
};

const backgrounds = {
  transparent: 'bg-transparent',
  dark: 'bg-black',
  gradient: 'bg-gradient-to-b from-black via-purple-950/5 to-black'
};

export function MobileSection({
  children,
  className,
  spacing = 'md',
  background = 'transparent'
}: MobileSectionProps) {
  return (
    <section
      className={cn(
        'relative w-full',
        spacings[spacing],
        backgrounds[background],
        className
      )}
    >
      {children}
    </section>
  );
}
