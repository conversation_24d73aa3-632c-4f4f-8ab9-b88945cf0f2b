'use client';

import { useState, useEffect } from 'react';

interface MobileDetection {
  isMobile: boolean;
  isTablet: boolean;
  isDesktop: boolean;
  screenWidth: number;
  screenHeight: number;
  isTouchDevice: boolean;
  orientation: 'portrait' | 'landscape';
  devicePixelRatio: number;
}

export function useMobile(): MobileDetection {
  const [mobileState, setMobileState] = useState<MobileDetection>({
    isMobile: false,
    isTablet: false,
    isDesktop: true,
    screenWidth: 1024,
    screenHeight: 768,
    isTouchDevice: false,
    orientation: 'landscape',
    devicePixelRatio: 1
  });

  useEffect(() => {
    const updateMobileState = () => {
      const width = window.innerWidth;
      const height = window.innerHeight;
      const isTouchDevice = 'ontouchstart' in window || navigator.maxTouchPoints > 0;
      const devicePixelRatio = window.devicePixelRatio || 1;
      
      setMobileState({
        isMobile: width < 768,
        isTablet: width >= 768 && width < 1024,
        isDesktop: width >= 1024,
        screenWidth: width,
        screenHeight: height,
        isTouchDevice,
        orientation: width > height ? 'landscape' : 'portrait',
        devicePixelRatio
      });
    };

    // Initial check
    updateMobileState();

    // Listen for resize events
    window.addEventListener('resize', updateMobileState);
    window.addEventListener('orientationchange', updateMobileState);

    return () => {
      window.removeEventListener('resize', updateMobileState);
      window.removeEventListener('orientationchange', updateMobileState);
    };
  }, []);

  return mobileState;
}

// Hook for responsive breakpoints
export function useBreakpoint() {
  const [breakpoint, setBreakpoint] = useState<'xs' | 'sm' | 'md' | 'lg' | 'xl' | '2xl'>('lg');

  useEffect(() => {
    const updateBreakpoint = () => {
      const width = window.innerWidth;
      
      if (width < 480) {
        setBreakpoint('xs');
      } else if (width < 640) {
        setBreakpoint('sm');
      } else if (width < 768) {
        setBreakpoint('md');
      } else if (width < 1024) {
        setBreakpoint('lg');
      } else if (width < 1280) {
        setBreakpoint('xl');
      } else {
        setBreakpoint('2xl');
      }
    };

    updateBreakpoint();
    window.addEventListener('resize', updateBreakpoint);

    return () => window.removeEventListener('resize', updateBreakpoint);
  }, []);

  return breakpoint;
}

// Hook for mobile-specific performance optimizations
export function useMobilePerformance() {
  const { isMobile, isTouchDevice, devicePixelRatio, screenWidth } = useMobile();

  // Detect low-end devices
  const isLowEndDevice = isMobile && (
    devicePixelRatio <= 1.5 ||
    screenWidth <= 375 ||
    navigator.hardwareConcurrency <= 2
  );

  return {
    // Reduce animation complexity on mobile
    shouldReduceMotion: isMobile && (devicePixelRatio > 2 || isLowEndDevice),

    // Optimize image loading
    imageQuality: isLowEndDevice ? 60 : isMobile ? 75 : 90,

    // Reduce particle count for mobile
    particleCount: isLowEndDevice ? 15 : isMobile ? 30 : 80,

    // Optimize animation frame rate
    animationFrameRate: isLowEndDevice ? 24 : isMobile ? 30 : 60,

    // Touch-specific optimizations
    touchOptimizations: isTouchDevice,

    // Lazy loading threshold
    lazyLoadingThreshold: isMobile ? '100px' : '200px',

    // Additional mobile optimizations
    enableBlur: !isLowEndDevice,
    enableShadows: !isLowEndDevice,
    enableGradients: !isLowEndDevice,
    maxConcurrentAnimations: isLowEndDevice ? 2 : isMobile ? 4 : 8,

    // Network-aware optimizations
    prefetchImages: !isMobile || navigator.connection?.effectiveType === '4g',
    enableVideoAutoplay: !isMobile,

    // Memory management
    enableVirtualization: isMobile,
    maxCacheSize: isLowEndDevice ? 10 : isMobile ? 20 : 50,

    // Device capabilities
    isLowEndDevice,
    supportsWebP: typeof window !== 'undefined' &&
      document.createElement('canvas').toDataURL('image/webp').indexOf('data:image/webp') === 0,
    supportsAvif: typeof window !== 'undefined' &&
      document.createElement('canvas').toDataURL('image/avif').indexOf('data:image/avif') === 0
  };
}

// Hook for mobile viewport management
export function useMobileViewport() {
  const [viewportHeight, setViewportHeight] = useState(0);
  const [isKeyboardOpen, setIsKeyboardOpen] = useState(false);

  useEffect(() => {
    const updateViewport = () => {
      const vh = window.innerHeight;
      setViewportHeight(vh);
      
      // Detect virtual keyboard on mobile
      if (window.visualViewport) {
        const keyboardHeight = window.innerHeight - window.visualViewport.height;
        setIsKeyboardOpen(keyboardHeight > 150);
      }
    };

    updateViewport();
    window.addEventListener('resize', updateViewport);
    
    if (window.visualViewport) {
      window.visualViewport.addEventListener('resize', updateViewport);
    }

    return () => {
      window.removeEventListener('resize', updateViewport);
      if (window.visualViewport) {
        window.visualViewport.removeEventListener('resize', updateViewport);
      }
    };
  }, []);

  return {
    viewportHeight,
    isKeyboardOpen,
    // CSS custom property for 100vh fix on mobile
    cssVh: `${viewportHeight * 0.01}px`
  };
}

// Hook for touch gesture detection
export function useTouchGestures() {
  const [touchState, setTouchState] = useState({
    isSwipeLeft: false,
    isSwipeRight: false,
    isSwipeUp: false,
    isSwipeDown: false,
    isPinching: false,
    touchCount: 0
  });

  useEffect(() => {
    let startX = 0;
    let startY = 0;
    let startDistance = 0;

    const handleTouchStart = (e: TouchEvent) => {
      const touches = e.touches;
      setTouchState(prev => ({ ...prev, touchCount: touches.length }));

      if (touches.length === 1) {
        startX = touches[0].clientX;
        startY = touches[0].clientY;
      } else if (touches.length === 2) {
        startDistance = Math.hypot(
          touches[0].clientX - touches[1].clientX,
          touches[0].clientY - touches[1].clientY
        );
      }
    };

    const handleTouchMove = (e: TouchEvent) => {
      const touches = e.touches;

      if (touches.length === 2) {
        const currentDistance = Math.hypot(
          touches[0].clientX - touches[1].clientX,
          touches[0].clientY - touches[1].clientY
        );
        setTouchState(prev => ({ 
          ...prev, 
          isPinching: Math.abs(currentDistance - startDistance) > 10 
        }));
      }
    };

    const handleTouchEnd = (e: TouchEvent) => {
      const changedTouches = e.changedTouches;
      
      if (changedTouches.length === 1) {
        const endX = changedTouches[0].clientX;
        const endY = changedTouches[0].clientY;
        const deltaX = endX - startX;
        const deltaY = endY - startY;
        const minSwipeDistance = 50;

        if (Math.abs(deltaX) > minSwipeDistance || Math.abs(deltaY) > minSwipeDistance) {
          if (Math.abs(deltaX) > Math.abs(deltaY)) {
            // Horizontal swipe
            setTouchState(prev => ({
              ...prev,
              isSwipeLeft: deltaX < 0,
              isSwipeRight: deltaX > 0
            }));
          } else {
            // Vertical swipe
            setTouchState(prev => ({
              ...prev,
              isSwipeUp: deltaY < 0,
              isSwipeDown: deltaY > 0
            }));
          }

          // Reset swipe states after a short delay
          setTimeout(() => {
            setTouchState(prev => ({
              ...prev,
              isSwipeLeft: false,
              isSwipeRight: false,
              isSwipeUp: false,
              isSwipeDown: false
            }));
          }, 100);
        }
      }

      setTouchState(prev => ({ 
        ...prev, 
        touchCount: e.touches.length,
        isPinching: false 
      }));
    };

    document.addEventListener('touchstart', handleTouchStart, { passive: true });
    document.addEventListener('touchmove', handleTouchMove, { passive: true });
    document.addEventListener('touchend', handleTouchEnd, { passive: true });

    return () => {
      document.removeEventListener('touchstart', handleTouchStart);
      document.removeEventListener('touchmove', handleTouchMove);
      document.removeEventListener('touchend', handleTouchEnd);
    };
  }, []);

  return touchState;
}
