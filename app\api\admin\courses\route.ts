import { NextRequest, NextResponse } from 'next/server';
import { createSupabaseServerClient } from '@/lib/supabase/client';

export async function GET(request: NextRequest) {
  try {
    const serverClient = createSupabaseServerClient();
    
    // Check admin access
    const { data: { session }, error: sessionError } = await serverClient.auth.getSession();
    if (sessionError || !session) {
      return NextResponse.json({ error: 'Not authenticated' }, { status: 401 });
    }

    const { data: user } = await serverClient
      .from('users')
      .select('role')
      .eq('id', session.user.id)
      .single();

    if (!user || user.role !== 'ADMIN') {
      return NextResponse.json({ error: 'Access denied' }, { status: 403 });
    }

    // Get courses with category information
    const { data: courses, error } = await serverClient
      .from('courses')
      .select(`
        *,
        category:course_categories(name, color),
        instructor_user:users!instructor_id(name)
      `)
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Error fetching courses:', error);
      return NextResponse.json({ error: 'Failed to fetch courses' }, { status: 500 });
    }

    return NextResponse.json({ courses: courses || [] });

  } catch (error: any) {
    console.error('Courses API error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const serverClient = createSupabaseServerClient();
    
    // Check admin access
    const { data: { session }, error: sessionError } = await serverClient.auth.getSession();
    if (sessionError || !session) {
      return NextResponse.json({ error: 'Not authenticated' }, { status: 401 });
    }

    const { data: user } = await serverClient
      .from('users')
      .select('role')
      .eq('id', session.user.id)
      .single();

    if (!user || user.role !== 'ADMIN') {
      return NextResponse.json({ error: 'Access denied' }, { status: 403 });
    }

    const courseData = await request.json();

    // Create the course
    const { data: course, error } = await serverClient
      .from('courses')
      .insert({
        title: courseData.title,
        description: courseData.description,
        price: courseData.price,
        instructor: courseData.instructor,
        instructor_id: courseData.instructor_id || session.user.id,
        category_id: courseData.category_id,
        thumbnail_url: courseData.thumbnail_url,
        video_url: courseData.video_url,
        duration_minutes: courseData.duration_minutes || 0,
        difficulty_level: courseData.difficulty_level || 'beginner',
        is_published: courseData.is_published || false,
        is_featured: courseData.is_featured || false,
        tags: courseData.tags || [],
        prerequisites: courseData.prerequisites || [],
        learning_outcomes: courseData.learning_outcomes || []
      })
      .select()
      .single();

    if (error) {
      console.error('Error creating course:', error);
      return NextResponse.json({ error: 'Failed to create course' }, { status: 500 });
    }

    return NextResponse.json({ course });

  } catch (error: any) {
    console.error('Create course error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
