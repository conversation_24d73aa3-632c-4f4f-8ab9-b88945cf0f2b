'use client';

import { motion } from 'framer-motion';
import { Check, X } from 'lucide-react';
import { cn } from '@/lib/utils';

interface PasswordStrengthProps {
  password: string;
  show: boolean;
}

interface PasswordRequirement {
  label: string;
  test: (password: string) => boolean;
}

const requirements: PasswordRequirement[] = [
  {
    label: 'At least 8 characters',
    test: (password) => password.length >= 8
  },
  {
    label: 'Contains uppercase letter',
    test: (password) => /[A-Z]/.test(password)
  },
  {
    label: 'Contains lowercase letter',
    test: (password) => /[a-z]/.test(password)
  },
  {
    label: 'Contains number',
    test: (password) => /\d/.test(password)
  },
  {
    label: 'Contains special character',
    test: (password) => /[!@#$%^&*(),.?":{}|<>]/.test(password)
  }
];

export default function PasswordStrength({ password, show }: PasswordStrengthProps) {
  const getStrengthScore = () => {
    return requirements.reduce((score, req) => {
      return score + (req.test(password) ? 1 : 0);
    }, 0);
  };

  const getStrengthLabel = (score: number) => {
    if (score === 0) return 'Very Weak';
    if (score <= 2) return 'Weak';
    if (score <= 3) return 'Fair';
    if (score <= 4) return 'Good';
    return 'Strong';
  };

  const getStrengthColor = (score: number) => {
    if (score === 0) return 'bg-gray-600';
    if (score <= 2) return 'bg-red-500';
    if (score <= 3) return 'bg-yellow-500';
    if (score <= 4) return 'bg-blue-500';
    return 'bg-green-500';
  };

  const score = getStrengthScore();
  const strengthLabel = getStrengthLabel(score);
  const strengthColor = getStrengthColor(score);

  if (!show || !password) return null;

  return (
    <motion.div
      initial={{ opacity: 0, height: 0 }}
      animate={{ opacity: 1, height: 'auto' }}
      exit={{ opacity: 0, height: 0 }}
      transition={{ duration: 0.3 }}
      className="mt-3 p-4 bg-gray-900/30 rounded-lg border border-gray-700"
    >
      {/* Strength Indicator */}
      <div className="mb-3">
        <div className="flex justify-between items-center mb-2">
          <span className="text-sm text-gray-400">Password Strength</span>
          <span className={cn(
            "text-sm font-medium",
            score <= 2 ? "text-red-400" : score <= 4 ? "text-yellow-400" : "text-green-400"
          )}>
            {strengthLabel}
          </span>
        </div>
        
        <div className="w-full bg-gray-700 rounded-full h-2">
          <motion.div
            initial={{ width: 0 }}
            animate={{ width: `${(score / 5) * 100}%` }}
            transition={{ duration: 0.3 }}
            className={cn("h-2 rounded-full transition-colors duration-300", strengthColor)}
          />
        </div>
      </div>

      {/* Requirements List */}
      <div className="space-y-2">
        <p className="text-xs text-gray-400 mb-2">Password must contain:</p>
        {requirements.map((requirement, index) => {
          const isValid = requirement.test(password);
          
          return (
            <motion.div
              key={index}
              initial={{ opacity: 0, x: -10 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: index * 0.1 }}
              className="flex items-center space-x-2"
            >
              <div className={cn(
                "w-4 h-4 rounded-full flex items-center justify-center",
                isValid ? "bg-green-500" : "bg-gray-600"
              )}>
                {isValid ? (
                  <Check className="w-3 h-3 text-white" />
                ) : (
                  <X className="w-3 h-3 text-gray-400" />
                )}
              </div>
              <span className={cn(
                "text-xs",
                isValid ? "text-green-400" : "text-gray-400"
              )}>
                {requirement.label}
              </span>
            </motion.div>
          );
        })}
      </div>
    </motion.div>
  );
}
