// Company data structure for incubator participants

export interface Mentor {
  id: string;
  name: string;
  role: string;
  expertise: string[];
  image: string;
  bio: string;
  email: string;
  linkedin?: string;
  calendlyUrl?: string;
}

export interface Milestone {
  id: string;
  title: string;
  description: string;
  dueDate: string;
  status: 'pending' | 'in-progress' | 'completed' | 'overdue';
  priority: 'low' | 'medium' | 'high';
  resources?: string[];
}

export interface ProgramTrack {
  id: string;
  name: string;
  description: string;
  duration: string;
  stage: string;
  color: string;
  icon: string;
}

export interface FounderProfile {
  id: string;
  name: string;
  role: string;
  photo: string;
  bio: string;
  education: string[];
  previousExperience: string[];
  expertise: string[];
  linkedin?: string;
  twitter?: string;
}

export interface ProductFeature {
  name: string;
  description: string;
  icon: string;
}

export interface CompanyTimeline {
  date: string;
  title: string;
  description: string;
  type: 'founding' | 'product' | 'funding' | 'milestone' | 'award' | 'media';
  icon: string;
}

export interface FundingRound {
  round: string;
  amount: string;
  date: string;
  investors: string[];
  valuation?: string;
  purpose: string;
}

export interface CompetitorAnalysis {
  name: string;
  description: string;
  strengths: string[];
  weaknesses: string[];
  marketShare?: string;
}

export interface Company {
  id: string;
  name: string;
  logo: string;
  description: string;
  industry: string;
  website?: string;
  foundedYear: number;
  founders: string[];
  programTrack: ProgramTrack;
  currentStage: string;
  joinDate: string;
  mentors: Mentor[];
  milestones: Milestone[];
  resources: {
    courses: string[];
    documents: string[];
    tools: string[];
  };
  contact: {
    primaryContact: string;
    email: string;
    phone?: string;
  };
  progress: {
    overall: number;
    milestones: number;
    courses: number;
  };
  nextSteps: string[];
  faq: {
    question: string;
    answer: string;
  }[];

  // Enhanced profile data
  profile: {
    overview: {
      foundingStory: string;
      mission: string;
      vision: string;
      coreValues: string[];
      businessModel: string;
      valueProposition: string;
      targetMarket: string;
      customerSegments: string[];
      competitiveAdvantages: string[];
    };

    foundersAndLeadership: {
      founders: FounderProfile[];
      leadership: FounderProfile[];
      advisors: FounderProfile[];
    };

    productService: {
      overview: string;
      features: ProductFeature[];
      technicalSpecs: string[];
      developmentTimeline: string;
      roadmap: string[];
      useCases: string[];
      testimonials: {
        customer: string;
        company: string;
        quote: string;
        role: string;
      }[];
    };

    marketAnalysis: {
      industryOverview: string;
      marketSize: string;
      targetDemographics: string[];
      competitors: CompetitorAnalysis[];
      marketPositioning: string;
      strategy: string[];
    };

    financial: {
      fundingHistory: FundingRound[];
      revenueModel: string;
      keyMetrics: {
        metric: string;
        value: string;
        description: string;
      }[];
      projections: string;
      investors: string[];
    };

    timeline: CompanyTimeline[];

    technology: {
      architecture: string;
      techStack: string[];
      intellectualProperty: string[];
      rdInitiatives: string[];
      partnerships: string[];
    };

    socialImpact: {
      environmentalImpact: string;
      sustainabilityEfforts: string[];
      socialResponsibility: string[];
      communityInvolvement: string[];
      esgCommitments: string[];
    };
  };
}

// Program tracks
export const programTracks: ProgramTrack[] = [
  {
    id: 'accelerator',
    name: 'Accelerator Program',
    description: '8-month intensive program for pre-seed and seed-stage startups',
    duration: '8 months',
    stage: 'Pre-seed to Seed',
    color: 'from-purple-500 to-blue-500',
    icon: '🚀'
  },
  {
    id: 'mongolian-idea',
    name: 'Mongolian Intellectual Idea',
    description: '3-month program for students and young entrepreneurs',
    duration: '3 months',
    stage: 'Idea to MVP',
    color: 'from-green-500 to-teal-500',
    icon: '💡'
  },
  {
    id: 'growth',
    name: 'Growth Program',
    description: '6-month program for scaling existing businesses',
    duration: '6 months',
    stage: 'Series A+',
    color: 'from-orange-500 to-red-500',
    icon: '📈'
  },
  {
    id: 'corporate',
    name: 'Corporate Innovation',
    description: '4-month program for corporate innovation projects',
    duration: '4 months',
    stage: 'Corporate',
    color: 'from-indigo-500 to-purple-500',
    icon: '🏢'
  }
];

// Mentors pool
export const mentorsPool: Mentor[] = [
  {
    id: 'mentor-1',
    name: 'Alexandra Chen',
    role: 'Business Strategy Mentor',
    expertise: ['Business Strategy', 'Fundraising', 'Market Expansion'],
    image: '/images/Team/team1.jpg',
    bio: 'Former VP at Goldman Sachs with 15+ years in venture capital and business strategy.',
    email: '<EMAIL>',
    linkedin: 'https://linkedin.com/in/alexandra-chen',
    calendlyUrl: 'https://calendly.com/alexandra-chen'
  },
  {
    id: 'mentor-2',
    name: 'Michael Rodriguez',
    role: 'Technology Advisor',
    expertise: ['Product Development', 'Technical Architecture', 'Team Building'],
    image: '/images/Team/team2.jpg',
    bio: 'Former CTO at multiple successful startups, expert in scaling technical teams.',
    email: '<EMAIL>',
    linkedin: 'https://linkedin.com/in/michael-rodriguez',
    calendlyUrl: 'https://calendly.com/michael-rodriguez'
  },
  {
    id: 'mentor-3',
    name: 'Sarah Johnson',
    role: 'Marketing & Growth Mentor',
    expertise: ['Digital Marketing', 'Growth Hacking', 'Brand Strategy'],
    image: '/images/Team/team3.jpg',
    bio: 'Former Head of Growth at unicorn startups, specialized in user acquisition.',
    email: '<EMAIL>',
    linkedin: 'https://linkedin.com/in/sarah-johnson',
    calendlyUrl: 'https://calendly.com/sarah-johnson'
  },
  {
    id: 'mentor-4',
    name: 'David Park',
    role: 'Operations Mentor',
    expertise: ['Operations', 'Process Optimization', 'Supply Chain'],
    image: '/images/Team/team4.jpg',
    bio: 'Operations expert who scaled multiple companies from startup to IPO.',
    email: '<EMAIL>',
    linkedin: 'https://linkedin.com/in/david-park',
    calendlyUrl: 'https://calendly.com/david-park'
  }
];

// Authentic Inno Hub companies data
export const companies: Company[] = [
  {
    id: 'artisy-hub',
    name: 'Artisy Hub',
    logo: '/images/programs/1.jpg',
    description: 'уран бүтээлчдийн нэгдэл 2023 онд үүсгэн байгуулагдсан.',
    industry: 'Creative Arts',
    website: 'https://www.artisyhub.mn/home',
    foundedYear: 2023,
    founders: ['Artisy Hub Team'],
    programTrack: programTracks[0], // Accelerator Program
    currentStage: 'Pre-seed',
    joinDate: '2023-01-01',
    mentors: [mentorsPool[0], mentorsPool[1]], // Alexandra Chen, Michael Rodriguez
    milestones: [
      {
        id: 'milestone-1',
        title: 'App Store Launch',
        description: 'Successfully launched on Apple App Store and Google Play Store',
        dueDate: '2024-01-15',
        status: 'completed',
        priority: 'high',
        resources: ['App Development Guide', 'Store Submission Guidelines']
      },
      {
        id: 'milestone-2',
        title: 'User Base Growth',
        description: 'Grow user base and engagement on the platform',
        dueDate: '2024-06-01',
        status: 'in-progress',
        priority: 'high',
        resources: ['Marketing Strategy Guide', 'User Acquisition Templates']
      },
      {
        id: 'milestone-3',
        title: 'Monetization Strategy',
        description: 'Implement revenue streams and business model',
        dueDate: '2024-08-30',
        status: 'pending',
        priority: 'medium',
        resources: ['Business Model Canvas', 'Revenue Strategy Template']
      }
    ],
    resources: {
      courses: ['Creative Business Development', 'Mobile App Marketing', 'Digital Platform Management'],
      documents: ['Business Plan Template', 'Legal Documents', 'App Store Guidelines'],
      tools: ['Slack Workspace', 'Google Workspace', 'Notion Dashboard']
    },
    contact: {
      primaryContact: 'Artisy Hub Team',
      email: '<EMAIL>',
      phone: '+976 9999 0001'
    },
    progress: {
      overall: 75,
      milestones: 80,
      courses: 70
    },
    nextSteps: [
      'Expand user base',
      'Develop monetization features',
      'Build artist community',
      'Launch marketing campaigns'
    ],
    faq: [
      {
        question: 'How can I download the Artisy Hub app?',
        answer: 'You can download the app from Apple App Store or Google Play Store. Links are available on our website and social media pages.'
      },
      {
        question: 'What features does the app offer for artists?',
        answer: 'The app provides a platform for artists to showcase their work, connect with other artists, and build their creative community.'
      },
      {
        question: 'How can I get support for the app?',
        answer: 'You can contact us through our Facebook page or website for any technical support or questions about the platform.'
      }
    ],

    profile: {
      overview: {
        foundingStory: "Artisy Hub was founded in 2023 as a creative artists' community platform, bringing together artists from various disciplines to showcase their work and connect with fellow creatives.",
        mission: "To create a vibrant digital community where artists can showcase their talents, connect with peers, and grow their creative careers.",
        vision: "To become the leading creative platform in Mongolia, empowering artists with digital tools and community support.",
        coreValues: [
          "Creativity - Supporting artistic expression in all forms",
          "Community - Building connections between artists",
          "Innovation - Using technology to enhance creative processes",
          "Accessibility - Making art accessible to everyone",
          "Growth - Supporting artists' professional development"
        ],
        businessModel: "Mobile app platform with community features, artist showcases, and potential premium services for enhanced visibility and tools.",
        valueProposition: "Artisy Hub provides artists with a dedicated platform to showcase their work, connect with other artists, and build their creative community through mobile technology.",
        targetMarket: "Artists, creative professionals, art enthusiasts, and cultural organizations in Mongolia and surrounding regions.",
        customerSegments: [
          "Visual artists and painters",
          "Digital artists and designers",
          "Photographers and videographers",
          "Craft artists and makers",
          "Art students and emerging artists"
        ],
        competitiveAdvantages: [
          "First dedicated artist community app in Mongolia",
          "Local cultural understanding and language support",
          "Strong social media presence and community engagement",
          "Mobile-first approach for accessibility",
          "Focus on Mongolian and regional artistic traditions"
        ]
      },

      foundersAndLeadership: {
        founders: [
          {
            id: 'batbayar-ganbold',
            name: 'Batbayar Ganbold',
            role: 'CEO & Co-Founder',
            photo: '/images/Team/team1.jpg',
            bio: 'Agricultural engineer with 8+ years of experience in sustainable farming practices. Led multiple agricultural modernization projects across Mongolia and has deep expertise in crop optimization and irrigation systems.',
            education: [
              'M.S. Agricultural Engineering - National University of Mongolia',
              'B.S. Agricultural Sciences - Mongolian University of Life Sciences'
            ],
            previousExperience: [
              'Senior Agricultural Consultant - Ministry of Agriculture (2018-2023)',
              'Project Manager - Sustainable Agriculture Initiative (2016-2018)',
              'Agricultural Engineer - Green Valley Farms (2015-2016)'
            ],
            expertise: ['Sustainable Agriculture', 'Irrigation Systems', 'Crop Optimization', 'Project Management'],
            linkedin: 'https://linkedin.com/in/batbayar-ganbold'
          },
          {
            id: 'oyunaa-munkh',
            name: 'Oyunaa Munkh',
            role: 'CTO & Co-Founder',
            photo: '/images/Team/team2.jpg',
            bio: 'Data scientist and software engineer specializing in IoT applications and machine learning. Former tech lead at multiple startups with expertise in building scalable data platforms and AI-driven analytics systems.',
            education: [
              'M.S. Computer Science - National University of Mongolia',
              'B.S. Mathematics & Computer Science - Mongolian University of Science and Technology'
            ],
            previousExperience: [
              'Senior Data Scientist - TechMongolia (2020-2023)',
              'Software Engineer - DataFlow Solutions (2018-2020)',
              'Junior Developer - Innovation Labs (2017-2018)'
            ],
            expertise: ['Machine Learning', 'IoT Development', 'Data Analytics', 'Cloud Architecture'],
            linkedin: 'https://linkedin.com/in/oyunaa-munkh'
          }
        ],
        leadership: [
          {
            id: 'ganbaatar-bold',
            name: 'Ganbaatar Bold',
            role: 'Head of Sales & Marketing',
            photo: '/images/Team/team3.jpg',
            bio: 'Marketing professional with extensive experience in agricultural sector sales and business development. Joined EcoGrow in early 2024 to lead market expansion efforts.',
            education: [
              'MBA - International Business School Mongolia',
              'B.S. Business Administration - National University of Mongolia'
            ],
            previousExperience: [
              'Sales Director - AgriSupply Mongolia (2019-2024)',
              'Business Development Manager - FarmTech Solutions (2017-2019)'
            ],
            expertise: ['Agricultural Sales', 'Market Development', 'Strategic Partnerships', 'Customer Relations'],
            linkedin: 'https://linkedin.com/in/ganbaatar-bold'
          }
        ],
        advisors: [
          {
            id: 'dr-sarah-chen',
            name: 'Dr. Sarah Chen',
            role: 'Agricultural Technology Advisor',
            photo: '/images/Team/team4.jpg',
            bio: 'Former Director of Agricultural Innovation at FAO with 20+ years of experience in sustainable agriculture and technology adoption in developing countries.',
            education: [
              'Ph.D. Agricultural Sciences - UC Davis',
              'M.S. Environmental Engineering - Stanford University'
            ],
            previousExperience: [
              'Director of Agricultural Innovation - FAO (2015-2022)',
              'Senior Research Scientist - International Rice Research Institute (2010-2015)'
            ],
            expertise: ['Sustainable Agriculture', 'Technology Adoption', 'International Development', 'Policy Advisory'],
            linkedin: 'https://linkedin.com/in/dr-sarah-chen'
          }
        ]
      },

      productService: {
        overview: "EcoGrow's smart farming solution combines IoT sensors, AI analytics, and automated irrigation systems to optimize agricultural productivity while promoting sustainable farming practices.",
        features: [
          { name: 'Smart Irrigation', description: 'Automated watering based on soil moisture and weather data', icon: '💧' },
          { name: 'Crop Monitoring', description: 'Real-time monitoring of crop health and growth patterns', icon: '🌱' },
          { name: 'Weather Integration', description: 'Advanced weather forecasting and climate adaptation', icon: '🌤️' },
          { name: 'Data Analytics', description: 'AI-powered insights and predictive analytics dashboard', icon: '📊' }
        ],
        technicalSpecs: [
          'LoRaWAN connectivity for long-range communication',
          'Solar-powered sensors with 5-year battery life',
          'IP67 waterproof rating for harsh weather conditions',
          'Real-time data processing with edge computing'
        ],
        developmentTimeline: "18-month development cycle from prototype to commercial launch",
        roadmap: [
          'Q3 2024: Drone integration for aerial monitoring',
          'Q4 2024: Mobile app 2.0 with enhanced UI/UX',
          'Q1 2025: AI-powered pest detection system',
          'Q2 2025: Blockchain integration for supply chain tracking'
        ],
        useCases: [
          'Large-scale grain production optimization',
          'Greenhouse vegetable farming automation',
          'Fruit orchard management and monitoring',
          'Sustainable farming practice implementation'
        ],
        testimonials: [
          {
            customer: 'Munkh-Erdene',
            company: 'Golden Valley Farms',
            quote: 'EcoGrow has transformed our farming operations. We\'ve seen a 35% increase in yield while reducing water usage significantly.',
            role: 'Farm Manager'
          }
        ]
      },

      marketAnalysis: {
        industryOverview: "The AgriTech market in Central Asia is experiencing rapid growth driven by increasing demand for food security, climate change challenges, and government initiatives promoting agricultural modernization.",
        marketSize: "$2.5B total addressable market in Central Asia with 15% annual growth rate",
        targetDemographics: [
          'Commercial farms (500+ hectares)',
          'Medium-scale farms (50-500 hectares)',
          'Agricultural cooperatives',
          'Government agricultural programs'
        ],
        competitors: [
          {
            name: 'Traditional Farming Methods',
            description: 'Manual farming practices without technology integration',
            strengths: ['Low initial cost', 'Familiar to farmers'],
            weaknesses: ['Inefficient resource usage', 'Lower yields', 'Labor intensive'],
            marketShare: '70%'
          }
        ],
        marketPositioning: "Leading localized AgriTech solution for harsh climate conditions",
        strategy: [
          'Direct sales to large commercial farms',
          'Partnerships with agricultural cooperatives',
          'Government pilot programs',
          'Trade show and conference presence'
        ]
      },

      financial: {
        fundingHistory: [
          {
            round: 'Pre-seed',
            amount: '$100K',
            date: '2023-09',
            investors: ['Angel Investors', 'InnoHub Pre-seed Fund'],
            purpose: 'Product development and initial market validation'
          }
        ],
        revenueModel: "Subscription-based SaaS model with tiered pricing based on farm size and feature requirements",
        keyMetrics: [
          { metric: 'Monthly Recurring Revenue', value: '$15K', description: 'Current MRR from pilot customers' },
          { metric: 'Customer Acquisition Cost', value: '$500', description: 'Average cost to acquire new customer' },
          { metric: 'Customer Lifetime Value', value: '$5,000', description: 'Projected LTV over 3 years' }
        ],
        projections: "Targeting $500K ARR by end of 2024 with 100+ customers",
        investors: ['InnoHub Ventures', 'AgriTech Angels', 'Sustainable Future Fund']
      },

      timeline: [
        { date: '2023-01', title: 'Company Founded', description: 'EcoGrow officially founded by Batbayar and Oyunaa', type: 'founding', icon: '🚀' },
        { date: '2023-03', title: 'First Prototype', description: 'Completed initial IoT sensor prototype and testing', type: 'product', icon: '🔧' },
        { date: '2023-06', title: 'Pilot Program', description: 'Launched pilot program with 5 local farms', type: 'milestone', icon: '🌱' },
        { date: '2023-09', title: 'Pre-seed Funding', description: 'Raised $100K in pre-seed funding', type: 'funding', icon: '💰' },
        { date: '2024-01', title: 'InnoHub Accelerator', description: 'Accepted into InnoHub Accelerator Program', type: 'milestone', icon: '🎯' },
        { date: '2024-03', title: 'Product Launch', description: 'Official product launch with 20 commercial customers', type: 'product', icon: '🚀' }
      ],

      technology: {
        architecture: "Cloud-native architecture with edge computing capabilities for real-time data processing and decision making",
        techStack: ['Node.js', 'MongoDB', 'AWS IoT Core', 'TensorFlow', 'React Native'],
        intellectualProperty: [
          'Smart irrigation algorithm (Patent Pending)',
          'Multi-sensor data fusion technology (Trade Secret)'
        ],
        rdInitiatives: [
          'AI-powered crop health prediction',
          'Drone integration for aerial monitoring',
          'Blockchain for supply chain tracking'
        ],
        partnerships: [
          'AWS IoT Partner Program',
          'MongoDB Startup Program',
          'National University of Mongolia Research Collaboration'
        ]
      },

      socialImpact: {
        environmentalImpact: "Reducing agricultural water usage by 30% and chemical fertilizer usage by 25% through precision farming techniques",
        sustainabilityEfforts: [
          'Solar-powered IoT devices',
          'Water conservation programs',
          'Reduced chemical usage',
          'Carbon footprint tracking'
        ],
        socialResponsibility: [
          'Training programs for smallholder farmers',
          'Affordable pricing for developing communities',
          'Local employment creation',
          'Knowledge sharing initiatives'
        ],
        communityInvolvement: [
          'Partnership with agricultural universities',
          'Rural development programs',
          'Farmer education initiatives'
        ],
        esgCommitments: [
          'Carbon neutral operations by 2025',
          '50% women in leadership by 2026',
          'Transparent reporting standards',
          'Ethical business practices'
        ]
      }
    }
  },
  {
    id: 'share-wallet',
    name: 'Share Wallet',
    logo: '/images/programs/2.jpg',
    description: 'Өрхийн гэр бүлийн судалгааны платформ. Бид тоон мэдээлэлд суурилсан судалгааны бүтээгдэхүүнээрээ дамжуулан судалгааны зах зээлийг хүртээмжтэй болгох зорилготой компани юм.',
    industry: 'Research Platform',
    website: '',
    foundedYear: 2024,
    founders: ['Share Wallet Team'],
    programTrack: programTracks[1], // Mongolian Idea Program
    currentStage: 'Idea stage',
    joinDate: '2024-01-01',
    mentors: [mentorsPool[0], mentorsPool[2]],
    milestones: [
      {
        id: 'milestone-1',
        title: 'Market Research',
        description: 'Complete market analysis for family research platform',
        dueDate: '2024-03-15',
        status: 'in-progress',
        priority: 'high',
        resources: ['Market Research Template', 'Survey Design Guide']
      },
      {
        id: 'milestone-2',
        title: 'Platform Development',
        description: 'Develop MVP for research platform',
        dueDate: '2024-06-01',
        status: 'pending',
        priority: 'high',
        resources: ['Platform Development Guide', 'Technical Architecture']
      }
    ],
    resources: {
      courses: ['Research Methodology', 'Platform Development', 'Data Analytics'],
      documents: ['Business Plan Template', 'Research Ethics Guide', 'Data Privacy Policies'],
      tools: ['Slack Workspace', 'Google Workspace', 'Research Tools']
    },
    contact: {
      primaryContact: 'Share Wallet Team',
      email: '<EMAIL>',
      phone: '+976 9999 0002'
    },
    progress: {
      overall: 25,
      milestones: 30,
      courses: 20
    },
    nextSteps: [
      'Complete market validation',
      'Develop platform prototype',
      'Build research methodology',
      'Establish partnerships'
    ],
    faq: [
      {
        question: 'What type of research does the platform support?',
        answer: 'The platform focuses on family and household research, providing data-driven insights to make research more accessible.'
      },
      {
        question: 'How will data privacy be handled?',
        answer: 'We follow strict data privacy protocols and ethical research guidelines to protect participant information.'
      }
    ]
  },
  {
    id: 'milkman',
    name: 'Milkman',
    logo: '/images/programs/3.jpg',
    description: 'Эрүүл, чанартай, шинэхэн сүү, сүүн бүтээгдэхүүний үйлдвэрлэл, хүргэлт. Милкман Дэливери ХХК нь Сүү сүүн бүтээгдэхүүнийг үйлдвэрийн орчинд уламжлалт аргаар боловсруулан, хэрэглэгчиддээ шууд хүргэх чиглэлээр үйл ажиллагаагаа явуулдаг.',
    industry: 'Food & Beverage',
    website: 'https://milkman.mn',
    foundedYear: 2023,
    founders: ['Milkman Team'],
    programTrack: programTracks[2], // Growth Program
    currentStage: 'Early stage',
    joinDate: '2023-06-01',
    mentors: [mentorsPool[1], mentorsPool[3]], // Michael Rodriguez, David Park
    milestones: [
      {
        id: 'milestone-1',
        title: 'Product Line Expansion',
        description: 'Expand to 24 different dairy products',
        dueDate: '2024-01-15',
        status: 'completed',
        priority: 'high',
        resources: ['Product Development Guide', 'Quality Control Standards']
      },
      {
        id: 'milestone-2',
        title: 'Delivery Network Growth',
        description: 'Expand delivery network and customer base',
        dueDate: '2024-06-01',
        status: 'in-progress',
        priority: 'high',
        resources: ['Logistics Planning', 'Customer Acquisition Strategy']
      }
    ],
    resources: {
      courses: ['Food Safety Management', 'Supply Chain Optimization', 'Customer Service'],
      documents: ['Food Safety Protocols', 'Delivery Guidelines', 'Quality Standards'],
      tools: ['Slack Workspace', 'Delivery Management System', 'Quality Control Tools']
    },
    contact: {
      primaryContact: 'Milkman Team',
      email: '<EMAIL>',
      phone: '+976 9999 0003'
    },
    progress: {
      overall: 70,
      milestones: 75,
      courses: 65
    },
    nextSteps: [
      'Expand product distribution',
      'Improve delivery efficiency',
      'Develop brand marketing',
      'Scale production capacity'
    ],
    faq: [
      {
        question: 'What products does Milkman offer?',
        answer: 'We offer 24 different types of fresh dairy products, all produced using traditional methods in industrial settings.'
      },
      {
        question: 'How is product quality maintained?',
        answer: 'We follow strict quality control protocols and traditional production methods to ensure fresh, healthy dairy products.'
      }
    ]
  },
  {
    id: 'healthtech-pro',
    name: 'HealthTech Pro',
    logo: '/images/programs/plant-bulbs.jpg',
    description: 'AI-powered healthcare platform connecting patients with medical professionals through telemedicine.',
    industry: 'HealthTech',
    foundedYear: 2024,
    founders: ['Dr. Oyungerel Dash', 'Ganbaatar Tsedev'],
    programTrack: programTracks[1], // Mongolian Intellectual Idea
    currentStage: 'MVP Development',
    joinDate: '2024-03-01',
    mentors: [mentorsPool[1], mentorsPool[2]], // Michael Rodriguez, Sarah Johnson
    milestones: [
      {
        id: 'milestone-1',
        title: 'Healthcare Market Analysis',
        description: 'Analyze healthcare market opportunities and challenges',
        dueDate: '2024-03-30',
        status: 'completed',
        priority: 'medium',
        resources: ['Healthcare Market Report', 'Telemedicine Trends']
      },
      {
        id: 'milestone-2',
        title: 'MVP Development',
        description: 'Build basic telemedicine platform with video consultation',
        dueDate: '2024-05-15',
        status: 'in-progress',
        priority: 'high',
        resources: ['Healthcare App Development Guide', 'HIPAA Compliance']
      }
    ],
    resources: {
      courses: ['Healthcare Innovation', 'AI in Medicine', 'Telemedicine Basics'],
      documents: ['HIPAA Compliance Guide', 'Medical Device Regulations', 'Privacy Policies'],
      tools: ['Slack Workspace', 'Figma Pro', 'AWS Healthcare Credits']
    },
    contact: {
      primaryContact: 'Dr. Oyungerel Dash',
      email: '<EMAIL>',
      phone: '+976 9999 9012'
    },
    progress: {
      overall: 35,
      milestones: 40,
      courses: 30
    },
    nextSteps: [
      'Complete MVP development',
      'Obtain medical device certification',
      'Conduct pilot testing with clinics',
      'Develop go-to-market strategy'
    ],
    faq: [
      {
        question: 'What medical certifications do we need?',
        answer: 'You need medical device certification and healthcare data compliance. Our healthcare advisors can guide you through the process.'
      },
      {
        question: 'How can we find pilot customers?',
        answer: 'We have partnerships with local clinics and hospitals. Your mentor can facilitate introductions for pilot testing.'
      }
    ]
  },
  {
    id: 'renown-production',
    name: 'Renown Production Studio',
    logo: '/images/programs/4.jpg',
    description: '2024 оны 10-р сард байгуулагдсан бөгөөд шинэ үеийн чадварлаг, эрч хүчтэй, туршлагатай залуусаас бүрддэг. Манай студийн гол зорилго нь үр дүнтэй, үнэ цэнэтэй, хүн төвтэй үйлчилгээгэр үйлчилүүлэгчдийнхээ хүсэл, мөрөөдлийг биелүүлэхэд оршдог.',
    industry: 'Creative Production',
    website: '',
    foundedYear: 2024,
    founders: ['Renown Production Team'],
    programTrack: programTracks[0], // Accelerator Program
    currentStage: 'Pre-seed',
    joinDate: '2024-10-01',
    mentors: [mentorsPool[0], mentorsPool[2]],
    milestones: [
      {
        id: 'milestone-1',
        title: 'Studio Setup',
        description: 'Complete studio setup and equipment installation',
        dueDate: '2024-12-15',
        status: 'in-progress',
        priority: 'high',
        resources: ['Studio Setup Guide', 'Equipment List']
      },
      {
        id: 'milestone-2',
        title: 'First Client Projects',
        description: 'Complete first client projects and build portfolio',
        dueDate: '2025-03-01',
        status: 'pending',
        priority: 'high',
        resources: ['Project Management Tools', 'Client Communication Guide']
      }
    ],
    resources: {
      courses: ['Creative Business Management', 'Client Relations', 'Production Techniques'],
      documents: ['Business Plan Template', 'Client Contracts', 'Production Guidelines'],
      tools: ['Slack Workspace', 'Creative Software Suite', 'Project Management Tools']
    },
    contact: {
      primaryContact: 'Renown Production Team',
      email: '<EMAIL>',
      phone: '+976 9999 0004'
    },
    progress: {
      overall: 30,
      milestones: 35,
      courses: 25
    },
    nextSteps: [
      'Complete studio setup',
      'Build client portfolio',
      'Develop service offerings',
      'Establish market presence'
    ],
    faq: [
      {
        question: 'What services does the studio offer?',
        answer: 'We provide comprehensive creative production services focused on delivering effective, valuable, and human-centered solutions for our clients.'
      },
      {
        question: 'What makes your studio unique?',
        answer: 'Our team consists of talented, energetic, and experienced young professionals dedicated to turning client dreams into reality through creative solutions.'
      }
    ]
  },
  {
    id: 'amar-morning-drink',
    name: 'Амар Өглөөний унд',
    logo: '/images/programs/5.jpg',
    description: 'Завгүй амьдралын хэв маягаас шалтгаан өглөөний цайгүй өнжих тохиолдол Монголчуудын дунд түгээмэл байгаа бөгөөд тус асуудлыг шийдэхэд АМАР өглөөний унд туслах юм. Нэг удаагийн савлагаатай, шууд хэрэглэх зориулалттай өглөөний цай.',
    industry: 'Food & Beverage',
    website: '',
    foundedYear: 2024,
    founders: ['Амар Өглөөний унд Team'],
    programTrack: programTracks[0], // Accelerator Program
    currentStage: 'Pre-seed',
    joinDate: '2024-01-01',
    mentors: [mentorsPool[1], mentorsPool[3]],
    milestones: [
      {
        id: 'milestone-1',
        title: 'Product Development',
        description: 'Develop instant morning tea product with convenient packaging',
        dueDate: '2024-04-15',
        status: 'in-progress',
        priority: 'high',
        resources: ['Product Development Guide', 'Food Safety Standards']
      },
      {
        id: 'milestone-2',
        title: 'Market Testing',
        description: 'Conduct market testing and gather customer feedback',
        dueDate: '2024-07-01',
        status: 'pending',
        priority: 'medium',
        resources: ['Market Research Template', 'Customer Feedback Tools']
      }
    ],
    resources: {
      courses: ['Food Product Development', 'Packaging Design', 'Market Research'],
      documents: ['Food Safety Regulations', 'Packaging Guidelines', 'Marketing Strategy'],
      tools: ['Slack Workspace', 'Design Software', 'Market Research Tools']
    },
    contact: {
      primaryContact: 'Амар Өглөөний унд Team',
      email: '<EMAIL>',
      phone: '+976 9999 0005'
    },
    progress: {
      overall: 40,
      milestones: 45,
      courses: 35
    },
    nextSteps: [
      'Complete product formulation',
      'Design packaging solution',
      'Conduct market validation',
      'Develop distribution strategy'
    ],
    faq: [
      {
        question: 'What problem does АМАР solve?',
        answer: 'АМАР addresses the common issue of Mongolians skipping morning tea due to busy lifestyles by providing a convenient, instant morning drink solution.'
      },
      {
        question: 'What makes АМАР different from existing products?',
        answer: 'АМАР offers single-use packaging with instant preparation, filling a gap in the Mongolian market where similar convenient tea products are limited.'
      }
    ]
  }
];
