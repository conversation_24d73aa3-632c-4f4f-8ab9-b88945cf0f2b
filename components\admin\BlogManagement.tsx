'use client';

import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Badge } from '@/components/ui/badge';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Plus, 
  Edit, 
  Trash2, 
  Eye, 
  Search, 
  Filter,
  Calendar,
  User,
  Tag,
  FileText,
  BarChart3,
  Save,
  X
} from 'lucide-react';
import { useLanguage } from '@/lib/context/language-context';
import { useAuth } from '@/contexts/AuthContext';
import blogService, { BlogPost, CreateBlogPostData, UpdateBlogPostData } from '@/lib/services/blogService';

export default function BlogManagement() {
  const { language } = useLanguage();
  const { user } = useAuth();
  const [posts, setPosts] = useState<BlogPost[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [editingPost, setEditingPost] = useState<BlogPost | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);

  // Form state
  const [formData, setFormData] = useState<CreateBlogPostData>({
    title: '',
    titleMn: '',
    excerpt: '',
    excerptMn: '',
    content: '',
    contentMn: '',
    featuredImage: '',
    category: 'Entrepreneurship',
    tags: [],
    isPublished: false
  });

  const categories = [
    'Entrepreneurship', 'Technology', 'Innovation', 'Startup', 'Business', 'Education'
  ];

  useEffect(() => {
    loadPosts();
  }, [currentPage, searchTerm, selectedCategory]);

  const loadPosts = async () => {
    setIsLoading(true);
    try {
      // Try to load real data from Appwrite
      const result = await blogService.getAllPosts({
        page: currentPage,
        limit: 10,
        search: searchTerm || undefined,
        category: selectedCategory !== 'all' ? selectedCategory : undefined,
        sortBy: 'createdAt',
        sortOrder: 'desc'
      });

      setPosts(result.posts);
      setTotalPages(result.pages);
    } catch (error) {
      console.error('Failed to load posts from Appwrite:', error);

      // Set empty posts array - admin will create real blog posts
      setPosts([]);
      setTotalPages(1);
    } finally {
      setIsLoading(false);
    }
  };

  const handleCreatePost = async () => {
    if (!session?.user?.id) return;

    try {
      const newPost = await blogService.createPost(session.user.id, formData);
      if (newPost) {
        setPosts([newPost, ...posts]);
        setShowCreateModal(false);
        resetForm();
      }
    } catch (error) {
      console.error('Failed to create post:', error);
    }
  };

  const handleUpdatePost = async () => {
    if (!editingPost) return;

    try {
      const updatedPost = await blogService.updatePost({
        id: editingPost.id,
        ...formData
      });
      
      if (updatedPost) {
        setPosts(posts.map(post => post.id === editingPost.id ? updatedPost : post));
        setEditingPost(null);
        resetForm();
      }
    } catch (error) {
      console.error('Failed to update post:', error);
    }
  };

  const handleDeletePost = async (postId: string) => {
    if (!confirm('Are you sure you want to delete this post?')) return;

    try {
      const success = await blogService.deletePost(postId);
      if (success) {
        setPosts(posts.filter(post => post.id !== postId));
      }
    } catch (error) {
      console.error('Failed to delete post:', error);
    }
  };

  const resetForm = () => {
    setFormData({
      title: '',
      titleMn: '',
      excerpt: '',
      excerptMn: '',
      content: '',
      contentMn: '',
      featuredImage: '',
      category: 'Entrepreneurship',
      tags: [],
      isPublished: false
    });
  };

  const openEditModal = (post: BlogPost) => {
    setEditingPost(post);
    setFormData({
      title: post.title,
      titleMn: post.titleMn || '',
      excerpt: post.excerpt,
      excerptMn: post.excerptMn || '',
      content: post.content,
      contentMn: post.contentMn || '',
      featuredImage: post.featuredImage || '',
      category: post.category,
      tags: post.tags,
      isPublished: post.isPublished
    });
  };

  const handleTagsChange = (tagsString: string) => {
    const tags = tagsString.split(',').map(tag => tag.trim()).filter(tag => tag.length > 0);
    setFormData({ ...formData, tags });
  };

  const PostForm = () => (
    <div className="space-y-6 max-h-[80vh] overflow-y-auto">
      <Tabs defaultValue="english" className="space-y-4">
        <TabsList className="bg-black/40 border border-purple-500/20">
          <TabsTrigger value="english" className="data-[state=active]:bg-purple-500/20">
            English
          </TabsTrigger>
          <TabsTrigger value="mongolian" className="data-[state=active]:bg-purple-500/20">
            Mongolian
          </TabsTrigger>
        </TabsList>

        <TabsContent value="english" className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="title" className="text-white">Title *</Label>
            <Input
              id="title"
              value={formData.title}
              onChange={(e) => setFormData({ ...formData, title: e.target.value })}
              placeholder="Enter post title..."
              className="bg-black/40 border-purple-500/20 text-white"
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="excerpt" className="text-white">Excerpt *</Label>
            <Textarea
              id="excerpt"
              value={formData.excerpt}
              onChange={(e) => setFormData({ ...formData, excerpt: e.target.value })}
              placeholder="Brief description of the post..."
              rows={3}
              className="bg-black/40 border-purple-500/20 text-white"
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="content" className="text-white">Content *</Label>
            <Textarea
              id="content"
              value={formData.content}
              onChange={(e) => setFormData({ ...formData, content: e.target.value })}
              placeholder="Write your post content here..."
              rows={10}
              className="bg-black/40 border-purple-500/20 text-white"
            />
          </div>
        </TabsContent>

        <TabsContent value="mongolian" className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="titleMn" className="text-white">Title (Mongolian)</Label>
            <Input
              id="titleMn"
              value={formData.titleMn}
              onChange={(e) => setFormData({ ...formData, titleMn: e.target.value })}
              placeholder="Гарчиг..."
              className="bg-black/40 border-purple-500/20 text-white"
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="excerptMn" className="text-white">Excerpt (Mongolian)</Label>
            <Textarea
              id="excerptMn"
              value={formData.excerptMn}
              onChange={(e) => setFormData({ ...formData, excerptMn: e.target.value })}
              placeholder="Товч тайлбар..."
              rows={3}
              className="bg-black/40 border-purple-500/20 text-white"
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="contentMn" className="text-white">Content (Mongolian)</Label>
            <Textarea
              id="contentMn"
              value={formData.contentMn}
              onChange={(e) => setFormData({ ...formData, contentMn: e.target.value })}
              placeholder="Нийтлэлийн агуулга..."
              rows={10}
              className="bg-black/40 border-purple-500/20 text-white"
            />
          </div>
        </TabsContent>
      </Tabs>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="category" className="text-white">Category</Label>
          <Select value={formData.category} onValueChange={(value) => setFormData({ ...formData, category: value })}>
            <SelectTrigger className="bg-black/40 border-purple-500/20 text-white">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              {categories.map((category) => (
                <SelectItem key={category} value={category}>{category}</SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        <div className="space-y-2">
          <Label htmlFor="featuredImage" className="text-white">Featured Image URL</Label>
          <Input
            id="featuredImage"
            value={formData.featuredImage}
            onChange={(e) => setFormData({ ...formData, featuredImage: e.target.value })}
            placeholder="https://example.com/image.jpg"
            className="bg-black/40 border-purple-500/20 text-white"
          />
        </div>
      </div>

      <div className="space-y-2">
        <Label htmlFor="tags" className="text-white">Tags (comma separated)</Label>
        <Input
          id="tags"
          value={formData.tags.join(', ')}
          onChange={(e) => handleTagsChange(e.target.value)}
          placeholder="entrepreneurship, startup, business"
          className="bg-black/40 border-purple-500/20 text-white"
        />
      </div>

      <div className="flex items-center space-x-2">
        <Switch
          id="published"
          checked={formData.isPublished}
          onCheckedChange={(checked) => setFormData({ ...formData, isPublished: checked })}
        />
        <Label htmlFor="published" className="text-white">Publish immediately</Label>
      </div>

      <div className="flex justify-end gap-3">
        <Button
          onClick={() => {
            setShowCreateModal(false);
            setEditingPost(null);
            resetForm();
          }}
          variant="outline"
          className="border-gray-500/30 text-gray-400 hover:bg-gray-500/10"
        >
          Cancel
        </Button>
        <Button
          onClick={editingPost ? handleUpdatePost : handleCreatePost}
          className="bg-purple-500 hover:bg-purple-600 text-white"
        >
          <Save className="h-4 w-4 mr-2" />
          {editingPost ? 'Update Post' : 'Create Post'}
        </Button>
      </div>
    </div>
  );

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-white">
            {language === 'mn' ? 'Блог удирдлага' : 'Blog Management'}
          </h1>
          <p className="text-gray-400">
            {language === 'mn' ? 'Нийтлэлүүдийг удирдах' : 'Manage blog posts and content'}
          </p>
        </div>
        
        <Button
          onClick={() => setShowCreateModal(true)}
          className="bg-purple-500 hover:bg-purple-600 text-white"
        >
          <Plus className="h-4 w-4 mr-2" />
          {language === 'mn' ? 'Шинэ нийтлэл' : 'New Post'}
        </Button>
      </div>

      {/* Filters */}
      <Card className="border-purple-500/20 bg-black/60 backdrop-blur-md">
        <CardContent className="p-4">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  placeholder={language === 'mn' ? 'Нийтлэл хайх...' : 'Search posts...'}
                  className="pl-10 bg-black/40 border-purple-500/20 text-white"
                />
              </div>
            </div>
            
            <Select value={selectedCategory} onValueChange={setSelectedCategory}>
              <SelectTrigger className="w-48 bg-black/40 border-purple-500/20 text-white">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Categories</SelectItem>
                {categories.map((category) => (
                  <SelectItem key={category} value={category}>{category}</SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Posts List */}
      <Card className="border-purple-500/20 bg-black/60 backdrop-blur-md">
        <CardHeader>
          <CardTitle className="text-white flex items-center gap-2">
            <FileText className="h-5 w-5 text-purple-400" />
            {language === 'mn' ? 'Нийтлэлүүд' : 'Blog Posts'}
          </CardTitle>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <div className="space-y-4">
              {[1, 2, 3].map((i) => (
                <div key={i} className="animate-pulse">
                  <div className="h-20 bg-gray-700/50 rounded"></div>
                </div>
              ))}
            </div>
          ) : posts.length === 0 ? (
            <div className="text-center py-8">
              <FileText className="h-12 w-12 text-gray-500 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-white mb-2">No posts found</h3>
              <p className="text-gray-400">Create your first blog post to get started.</p>
            </div>
          ) : (
            <div className="space-y-4">
              {posts.map((post) => (
                <motion.div
                  key={post.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  className="p-4 bg-gray-800/30 rounded-lg border border-gray-700/30"
                >
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center gap-3 mb-2">
                        <h3 className="text-white font-semibold">{post.title}</h3>
                        <Badge 
                          variant={post.isPublished ? "default" : "secondary"}
                          className={post.isPublished ? "bg-green-500/20 text-green-400" : "bg-gray-500/20 text-gray-400"}
                        >
                          {post.isPublished ? 'Published' : 'Draft'}
                        </Badge>
                      </div>
                      
                      <p className="text-gray-400 text-sm mb-3 line-clamp-2">{post.excerpt}</p>
                      
                      <div className="flex items-center gap-4 text-xs text-gray-500">
                        <div className="flex items-center gap-1">
                          <User className="h-3 w-3" />
                          <span>{post.author.name}</span>
                        </div>
                        <div className="flex items-center gap-1">
                          <Calendar className="h-3 w-3" />
                          <span>{new Date(post.createdAt).toLocaleDateString()}</span>
                        </div>
                        <div className="flex items-center gap-1">
                          <Eye className="h-3 w-3" />
                          <span>{post.views} views</span>
                        </div>
                        <div className="flex items-center gap-1">
                          <Tag className="h-3 w-3" />
                          <span>{post.category}</span>
                        </div>
                      </div>
                      
                      {post.tags.length > 0 && (
                        <div className="flex flex-wrap gap-1 mt-2">
                          {post.tags.map((tag) => (
                            <Badge key={tag} variant="outline" className="text-xs border-purple-500/30 text-purple-400">
                              {tag}
                            </Badge>
                          ))}
                        </div>
                      )}
                    </div>
                    
                    <div className="flex items-center gap-2 ml-4">
                      <Button
                        onClick={() => openEditModal(post)}
                        variant="ghost"
                        size="sm"
                        className="text-blue-400 hover:text-blue-300 hover:bg-blue-500/10"
                      >
                        <Edit className="h-4 w-4" />
                      </Button>
                      <Button
                        onClick={() => handleDeletePost(post.id)}
                        variant="ghost"
                        size="sm"
                        className="text-red-400 hover:text-red-300 hover:bg-red-500/10"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Create/Edit Post Modal */}
      <Dialog open={showCreateModal || !!editingPost} onOpenChange={(open) => {
        if (!open) {
          setShowCreateModal(false);
          setEditingPost(null);
          resetForm();
        }
      }}>
        <DialogContent className="bg-black/95 border-purple-500/20 text-white max-w-4xl">
          <DialogHeader>
            <DialogTitle>
              {editingPost ? 'Edit Post' : 'Create New Post'}
            </DialogTitle>
          </DialogHeader>
          <PostForm />
        </DialogContent>
      </Dialog>
    </div>
  );
}
