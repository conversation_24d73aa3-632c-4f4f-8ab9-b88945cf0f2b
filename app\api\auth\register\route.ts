import { NextRequest, NextResponse } from 'next/server';
import { createSupabaseServerClient } from '@/lib/supabase/client';

export async function POST(request: NextRequest) {
  try {
    console.log('API route called');
    console.log('Environment check:', {
      url: !!process.env.NEXT_PUBLIC_SUPABASE_URL,
      serviceKey: !!process.env.SUPABASE_SERVICE_ROLE_KEY
    });

    const { email, password, name, role = 'STUDENT' } = await request.json();

    console.log('Registration attempt:', { email, name, role });

    if (!email || !password || !name) {
      console.log('Missing required fields:', { email: !!email, password: !!password, name: !!name });
      return NextResponse.json(
        { success: false, error: 'Missing required fields' },
        { status: 400 }
      );
    }

    console.log('Creating Supabase server client...');
    const serverClient = createSupabaseServerClient();
    console.log('Server client created successfully');

    // Check if user already exists in our database
    console.log('Checking if user exists...');
    const { data: existingUser } = await serverClient
      .from('users')
      .select('id, email, role')
      .eq('email', email)
      .single();

    if (existingUser) {
      console.log('User already exists:', existingUser);
      return NextResponse.json(
        { success: false, error: 'User with this email already exists' },
        { status: 400 }
      );
    }

    // Sign up with Supabase Auth using regular signup (not admin)
    console.log('Creating auth user...');
    const { data: authData, error: authError } = await serverClient.auth.signUp({
      email,
      password,
      options: {
        data: {
          name,
          role
        }
      }
    });

    if (authError) {
      console.error('Auth error:', authError);
      // Handle case where auth user exists but profile doesn't
      if (authError.message.includes('already registered')) {
        return NextResponse.json(
          { success: false, error: 'User with this email already exists' },
          { status: 400 }
        );
      }
      return NextResponse.json(
        { success: false, error: authError.message },
        { status: 400 }
      );
    }

    if (!authData.user) {
      return NextResponse.json(
        { success: false, error: 'Failed to create user' },
        { status: 500 }
      );
    }

    // Create user profile in our users table
    const { data: userData, error: userError } = await serverClient
      .from('users')
      .insert({
        id: authData.user.id,
        email,
        name,
        role,
        is_onboarded: false
      })
      .select()
      .single();

    if (userError) {
      console.error('Database error:', userError);
      // Clean up the auth user if profile creation fails
      await serverClient.auth.admin.deleteUser(authData.user.id);

      if (userError.code === '23505') { // Unique constraint violation
        return NextResponse.json(
          { success: false, error: 'User with this email already exists' },
          { status: 400 }
        );
      }

      return NextResponse.json(
        { success: false, error: `Database error: ${userError.message}` },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      user: {
        id: userData.id,
        email: userData.email,
        name: userData.name,
        role: userData.role,
        avatar: userData.avatar,
        bio: userData.bio,
        skills: userData.skills,
        interests: userData.interests,
        is_onboarded: userData.is_onboarded
      }
    });

  } catch (error: any) {
    console.error('Registration error:', error);
    return NextResponse.json(
      { success: false, error: error.message },
      { status: 500 }
    );
  }
}
