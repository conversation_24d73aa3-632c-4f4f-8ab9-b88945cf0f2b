'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Users,
  BookOpen,
  FileText,
  TrendingUp,
  Plus,
  Settings,
  BarChart3,
  Eye,
  Edit,
  Trash2,
  Loader2
} from 'lucide-react';
import Link from 'next/link';

interface DashboardStats {
  totalUsers: number;
  totalCourses: number;
  totalBlogs: number;
  totalEnrollments: number;
  recentUsers: any[];
  recentCourses: any[];
  recentBlogs: any[];
}

export default function AdminDashboard() {
  const [stats, setStats] = useState<DashboardStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [seeding, setSeeding] = useState(false);
  const router = useRouter();

  useEffect(() => {
    checkAdminAccess();
    loadDashboardData();
  }, []);

  const checkAdminAccess = async () => {
    try {
      const response = await fetch('/api/auth/check-admin');
      const result = await response.json();
      
      if (!result.isAdmin) {
        router.push('/admin/login');
        return;
      }
    } catch (error) {
      router.push('/admin/login');
    }
  };

  const loadDashboardData = async () => {
    try {
      const response = await fetch('/api/admin/dashboard-stats');
      if (response.ok) {
        const data = await response.json();
        setStats(data);
      } else {
        console.error('Failed to load dashboard data:', response.status, response.statusText);
        // Set default stats if API fails
        setStats({
          totalUsers: 0,
          totalCourses: 0,
          totalBlogs: 0,
          totalEnrollments: 0,
          recentUsers: [],
          recentCourses: [],
          recentBlogs: []
        });
      }
    } catch (error) {
      console.error('Failed to load dashboard data:', error);
      // Set default stats if API fails
      setStats({
        totalUsers: 0,
        totalCourses: 0,
        totalBlogs: 0,
        totalEnrollments: 0,
        recentUsers: [],
        recentCourses: [],
        recentBlogs: []
      });
    } finally {
      setLoading(false);
    }
  };

  const seedSampleData = async () => {
    setSeeding(true);
    try {
      const response = await fetch('/api/admin/seed-data', {
        method: 'POST'
      });

      if (response.ok) {
        // Reload dashboard data after seeding
        await loadDashboardData();
        alert('Sample data created successfully!');
      } else {
        alert('Failed to create sample data');
      }
    } catch (error) {
      console.error('Failed to seed data:', error);
      alert('Failed to create sample data');
    } finally {
      setSeeding(false);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-purple-900 via-black to-purple-900 flex items-center justify-center">
        <div className="text-white">Loading admin dashboard...</div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-900 via-black to-purple-900">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="flex justify-between items-center mb-8">
          <div>
            <h1 className="text-3xl font-bold text-white">Admin Dashboard</h1>
            <p className="text-gray-400">Manage your InnoHub platform</p>
          </div>
          <div className="flex gap-4">
            <Button asChild className="bg-purple-600 hover:bg-purple-700">
              <Link href="/admin/courses/new">
                <Plus className="w-4 h-4 mr-2" />
                New Course
              </Link>
            </Button>
            <Button asChild variant="outline" className="border-purple-500/20 text-white">
              <Link href="/admin/blogs/new">
                <Plus className="w-4 h-4 mr-2" />
                New Blog
              </Link>
            </Button>
            {(!stats || (stats.totalCourses === 0 && stats.totalBlogs === 0)) && (
              <Button
                onClick={seedSampleData}
                disabled={seeding}
                variant="outline"
                className="border-green-500/20 text-green-400 hover:bg-green-500/10"
              >
                {seeding ? (
                  <>
                    <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                    Creating...
                  </>
                ) : (
                  <>
                    <Plus className="w-4 h-4 mr-2" />
                    Add Sample Data
                  </>
                )}
              </Button>
            )}
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <Card className="bg-black/50 border-purple-500/20 backdrop-blur-xl">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-gray-400">Total Users</CardTitle>
              <Users className="h-4 w-4 text-purple-400" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-white">{stats?.totalUsers || 0}</div>
              <p className="text-xs text-gray-400">Registered users</p>
            </CardContent>
          </Card>

          <Card className="bg-black/50 border-purple-500/20 backdrop-blur-xl">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-gray-400">Total Courses</CardTitle>
              <BookOpen className="h-4 w-4 text-blue-400" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-white">{stats?.totalCourses || 0}</div>
              <p className="text-xs text-gray-400">Published courses</p>
            </CardContent>
          </Card>

          <Card className="bg-black/50 border-purple-500/20 backdrop-blur-xl">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-gray-400">Total Blogs</CardTitle>
              <FileText className="h-4 w-4 text-green-400" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-white">{stats?.totalBlogs || 0}</div>
              <p className="text-xs text-gray-400">Published articles</p>
            </CardContent>
          </Card>

          <Card className="bg-black/50 border-purple-500/20 backdrop-blur-xl">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-gray-400">Enrollments</CardTitle>
              <TrendingUp className="h-4 w-4 text-yellow-400" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-white">{stats?.totalEnrollments || 0}</div>
              <p className="text-xs text-gray-400">Course enrollments</p>
            </CardContent>
          </Card>
        </div>

        {/* Quick Actions */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
          <Card className="bg-black/50 border-purple-500/20 backdrop-blur-xl">
            <CardHeader>
              <CardTitle className="text-white">Course Management</CardTitle>
              <CardDescription className="text-gray-400">
                Create and manage courses
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-2">
              <Button asChild className="w-full justify-start" variant="ghost">
                <Link href="/admin/courses">
                  <BookOpen className="w-4 h-4 mr-2" />
                  All Courses
                </Link>
              </Button>
              <Button asChild className="w-full justify-start" variant="ghost">
                <Link href="/admin/courses/new">
                  <Plus className="w-4 h-4 mr-2" />
                  Create Course
                </Link>
              </Button>
              <Button asChild className="w-full justify-start" variant="ghost">
                <Link href="/admin/categories">
                  <Settings className="w-4 h-4 mr-2" />
                  Categories
                </Link>
              </Button>
            </CardContent>
          </Card>

          <Card className="bg-black/50 border-purple-500/20 backdrop-blur-xl">
            <CardHeader>
              <CardTitle className="text-white">Blog Management</CardTitle>
              <CardDescription className="text-gray-400">
                Create and manage blog posts
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-2">
              <Button asChild className="w-full justify-start" variant="ghost">
                <Link href="/admin/blogs">
                  <FileText className="w-4 h-4 mr-2" />
                  All Blogs
                </Link>
              </Button>
              <Button asChild className="w-full justify-start" variant="ghost">
                <Link href="/admin/blogs/new">
                  <Plus className="w-4 h-4 mr-2" />
                  Create Blog
                </Link>
              </Button>
              <Button asChild className="w-full justify-start" variant="ghost">
                <Link href="/admin/blogs/drafts">
                  <Edit className="w-4 h-4 mr-2" />
                  Drafts
                </Link>
              </Button>
            </CardContent>
          </Card>

          <Card className="bg-black/50 border-purple-500/20 backdrop-blur-xl">
            <CardHeader>
              <CardTitle className="text-white">Analytics & Users</CardTitle>
              <CardDescription className="text-gray-400">
                Monitor platform performance
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-2">
              <Button asChild className="w-full justify-start" variant="ghost">
                <Link href="/admin/analytics">
                  <BarChart3 className="w-4 h-4 mr-2" />
                  Analytics
                </Link>
              </Button>
              <Button asChild className="w-full justify-start" variant="ghost">
                <Link href="/admin/users">
                  <Users className="w-4 h-4 mr-2" />
                  User Management
                </Link>
              </Button>
              <Button asChild className="w-full justify-start" variant="ghost">
                <Link href="/admin/settings">
                  <Settings className="w-4 h-4 mr-2" />
                  Settings
                </Link>
              </Button>
            </CardContent>
          </Card>
        </div>

        {/* Recent Activity */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <Card className="bg-black/50 border-purple-500/20 backdrop-blur-xl">
            <CardHeader>
              <CardTitle className="text-white">Recent Courses</CardTitle>
            </CardHeader>
            <CardContent>
              {stats?.recentCourses?.length ? (
                <div className="space-y-3">
                  {stats.recentCourses.map((course: any) => (
                    <div key={course.id} className="flex items-center justify-between">
                      <div>
                        <p className="text-white font-medium">{course.title}</p>
                        <p className="text-sm text-gray-400">{course.instructor}</p>
                      </div>
                      <Badge variant={course.is_published ? "default" : "secondary"}>
                        {course.is_published ? "Published" : "Draft"}
                      </Badge>
                    </div>
                  ))}
                </div>
              ) : (
                <p className="text-gray-400">No courses yet</p>
              )}
            </CardContent>
          </Card>

          <Card className="bg-black/50 border-purple-500/20 backdrop-blur-xl">
            <CardHeader>
              <CardTitle className="text-white">Recent Blog Posts</CardTitle>
            </CardHeader>
            <CardContent>
              {stats?.recentBlogs?.length ? (
                <div className="space-y-3">
                  {stats.recentBlogs.map((blog: any) => (
                    <div key={blog.id} className="flex items-center justify-between">
                      <div>
                        <p className="text-white font-medium">{blog.title}</p>
                        <p className="text-sm text-gray-400">{new Date(blog.created_at).toLocaleDateString()}</p>
                      </div>
                      <Badge variant={blog.is_published ? "default" : "secondary"}>
                        {blog.is_published ? "Published" : "Draft"}
                      </Badge>
                    </div>
                  ))}
                </div>
              ) : (
                <p className="text-gray-400">No blog posts yet</p>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
