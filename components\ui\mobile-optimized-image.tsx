'use client';

import Image from 'next/image';
import { useState } from 'react';
import { motion } from 'framer-motion';
import { cn } from '@/lib/utils';
import { useMobile, useMobilePerformance } from '@/hooks/use-mobile';

interface MobileOptimizedImageProps {
  src: string;
  alt: string;
  width?: number;
  height?: number;
  className?: string;
  priority?: boolean;
  fill?: boolean;
  sizes?: string;
  quality?: number;
  placeholder?: 'blur' | 'empty';
  blurDataURL?: string;
  onLoad?: () => void;
  enableMotion?: boolean;
  motionProps?: any;
}

export function MobileOptimizedImage({
  src,
  alt,
  width,
  height,
  className,
  priority = false,
  fill = false,
  sizes,
  quality,
  placeholder = 'empty',
  blurDataURL,
  onLoad,
  enableMotion = false,
  motionProps = {}
}: MobileOptimizedImageProps) {
  const [isLoaded, setIsLoaded] = useState(false);
  const { isMobile } = useMobile();
  const { imageQuality, lazyLoadingThreshold } = useMobilePerformance();

  // Use mobile-optimized quality if not specified
  const optimizedQuality = quality || imageQuality;

  // Mobile-optimized sizes if not specified
  const optimizedSizes = sizes || (
    isMobile 
      ? '(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw'
      : '(max-width: 1200px) 50vw, 33vw'
  );

  const handleLoad = () => {
    setIsLoaded(true);
    onLoad?.();
  };

  const imageProps = {
    src,
    alt,
    quality: optimizedQuality,
    sizes: optimizedSizes,
    priority: priority,
    placeholder,
    blurDataURL,
    onLoad: handleLoad,
    className: cn(
      'transition-opacity duration-300',
      isLoaded ? 'opacity-100' : 'opacity-0',
      className
    ),
    ...(fill ? { fill: true } : { width, height })
  };

  if (enableMotion) {
    return (
      <motion.div
        className="relative overflow-hidden"
        initial={{ opacity: 0 }}
        animate={{ opacity: isLoaded ? 1 : 0 }}
        transition={{ duration: 0.3 }}
        {...motionProps}
      >
        <Image {...imageProps} />
      </motion.div>
    );
  }

  return <Image {...imageProps} />;
}

// Mobile-optimized lazy loading wrapper
interface LazyImageProps extends MobileOptimizedImageProps {
  threshold?: string;
}

export function LazyMobileImage({
  threshold,
  ...props
}: LazyImageProps) {
  const { lazyLoadingThreshold } = useMobilePerformance();
  const optimizedThreshold = threshold || lazyLoadingThreshold;

  return (
    <div 
      className="relative"
      style={{ 
        marginBottom: optimizedThreshold,
        marginTop: optimizedThreshold 
      }}
    >
      <MobileOptimizedImage
        {...props}
        priority={false}
      />
    </div>
  );
}

// Mobile-optimized background image component
interface MobileBackgroundImageProps {
  src: string;
  alt: string;
  className?: string;
  children?: React.ReactNode;
  overlay?: boolean;
  overlayOpacity?: number;
  quality?: number;
  priority?: boolean;
}

export function MobileBackgroundImage({
  src,
  alt,
  className,
  children,
  overlay = true,
  overlayOpacity = 0.5,
  quality,
  priority = false
}: MobileBackgroundImageProps) {
  const { isMobile } = useMobile();
  const { imageQuality } = useMobilePerformance();

  const optimizedQuality = quality || imageQuality;

  return (
    <div className={cn('relative overflow-hidden', className)}>
      <MobileOptimizedImage
        src={src}
        alt={alt}
        fill
        quality={optimizedQuality}
        priority={priority}
        sizes={isMobile ? '100vw' : '(max-width: 1200px) 100vw, 100vw'}
        className="object-cover"
      />
      
      {overlay && (
        <div 
          className="absolute inset-0 bg-black z-10"
          style={{ opacity: overlayOpacity }}
        />
      )}
      
      {children && (
        <div className="relative z-20">
          {children}
        </div>
      )}
    </div>
  );
}

// Mobile-optimized avatar component
interface MobileAvatarProps {
  src: string;
  alt: string;
  size?: 'sm' | 'md' | 'lg' | 'xl';
  className?: string;
  priority?: boolean;
}

const avatarSizes = {
  sm: 'w-8 h-8',
  md: 'w-12 h-12',
  lg: 'w-16 h-16',
  xl: 'w-24 h-24'
};

export function MobileAvatar({
  src,
  alt,
  size = 'md',
  className,
  priority = false
}: MobileAvatarProps) {
  const { imageQuality } = useMobilePerformance();

  return (
    <div className={cn(
      'relative rounded-full overflow-hidden',
      avatarSizes[size],
      className
    )}>
      <MobileOptimizedImage
        src={src}
        alt={alt}
        fill
        quality={imageQuality}
        priority={priority}
        sizes="(max-width: 768px) 96px, 128px"
        className="object-cover"
      />
    </div>
  );
}
