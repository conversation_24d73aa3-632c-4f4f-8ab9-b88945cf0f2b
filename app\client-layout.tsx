'use client';

import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mono } from "next/font/google";
import { NavBar } from "@/components/ui/tubelight-navbar";
import { Home, Briefcase, FileText, Newspaper, Handshake, Info } from 'lucide-react';
import Footer from "@/components/shared/Footer";
import { LanguageProvider } from "@/lib/context/language-context";
import InitialLoadingOverlay from "@/components/InitialLoadingOverlay";
import { useEffect, useState } from "react";
import Image from "next/image";
import { ShootingStars } from "@/components/ui/shooting-stars";
import { StarsBackground } from "@/components/ui/stars-background";
import GradientCursor from "@/components/ui/gradient-cursor"; // Added import
import { usePathname } from "next/navigation"; // Added for path checking
import { MobileLayoutWrapper } from "@/components/layout/mobile-layout-wrapper";


const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export default function ClientLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  const [mounted, setMounted] = useState(false);
  const [isCursorActive, setIsCursorActive] = useState(false); // Added state for cursor
  const pathname = usePathname(); // Get current path
  const isProgramsPath = mounted && pathname?.startsWith('/programs'); // Check if we're in the programs section
  const isCoursesPath = mounted && pathname?.startsWith('/courses'); // Check if we're in the courses section
  const isAuthPath = mounted && pathname?.startsWith('/auth'); // Check if we're in the auth section
  const isAdminPath = mounted && pathname?.startsWith('/admin'); // Check if we're in the admin section

  const navItems = [
    { name: 'Home', url: '/', icon: Home },
    { name: 'About', url: '/about', icon: Info },
    { name: 'Programs', url: '/programs', icon: Briefcase },
    { name: 'Success Stories', url: '/success-stories', icon: FileText },
    { name: 'Investors', url: '/investors', icon: Handshake },
    { name: 'News', url: '/news', icon: Newspaper },
  ];



  useEffect(() => {
    setMounted(true);
  }, []);

  if (!mounted) {
    // Return a minimal layout while client-side code is loading
    return (
      <html lang="en" className="scroll-smooth dark">
        <body
          className={`${geistSans.variable} ${geistMono.variable} antialiased min-h-screen flex flex-col bg-black text-white`}
        >
          <div className="fixed top-0 left-0 right-0 z-50 bg-black/80 backdrop-blur-md border-b border-primary/10">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4 flex items-center justify-between">
              <div className="flex items-center gap-2">
                <div className="relative w-10 h-10 rounded-full overflow-hidden">
                  <Image
                    src="/images/logo/innohub_logo.png"
                    alt="InnoHub Logo"
                    width={40}
                    height={40}
                    className="object-cover"
                  />
                </div>
                <span className="font-bold text-xl">INNO<span className="text-primary">HUB</span></span>
              </div>
            </div>
          </div>
          <div className="flex-1 mt-20 flex items-center justify-center">
            <div className="animate-pulse">
              <div className="h-8 w-32 bg-primary/20 rounded-full mb-4"></div>
              <div className="h-4 w-48 bg-gray-700/50 rounded mb-2"></div>
              <div className="h-4 w-40 bg-gray-700/50 rounded"></div>
            </div>
          </div>
        </body>
      </html>
    );
  }

  return (
    <html lang="en" className="scroll-smooth dark">
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased min-h-screen flex flex-col bg-black text-white relative`}
        onMouseEnter={() => setIsCursorActive(true)}
        onMouseLeave={() => setIsCursorActive(false)}
      >
        <MobileLayoutWrapper enableSafeArea enableKeyboardAdjustment>
          <StarsBackground /> {/* Added stars background */}
          <ShootingStars /> {/* Added shooting stars */}
          <LanguageProvider>
            <InitialLoadingOverlay />
            {/* Only render the main Navbar when not in the courses, auth, or admin sections */}
            {!isCoursesPath && !isAuthPath && !isAdminPath && <NavBar items={navItems} />}
            <main className="flex-1 relative z-10">{children}</main> {/* Ensure children are above background */}
            {/* Only render the Footer when not in the programs, courses, auth, admin, or investors sections to avoid duplicate footers */}
            {!isProgramsPath && !isCoursesPath && !isAuthPath && !isAdminPath && pathname !== '/investors' && <Footer />}
          </LanguageProvider>
          <GradientCursor isActive={isCursorActive} /> {/* Added GradientCursor */}

        </MobileLayoutWrapper>
      </body>
    </html>
  );
}
