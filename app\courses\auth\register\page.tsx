'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
import { motion } from 'framer-motion';
import Link from 'next/link';
import { Mail, Lock, User, UserPlus } from 'lucide-react';

import AuthLayout from '@/components/auth/AuthLayout';
import AuthInput from '@/components/auth/AuthInput';
import AuthButton from '@/components/auth/AuthButton';
import AuthAlert from '@/components/auth/AuthAlert';
import SocialAuth from '@/components/auth/SocialAuth';
import PasswordStrength from '@/components/auth/PasswordStrength';

export default function CoursesRegisterPage() {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    password: '',
    confirmPassword: '',
    agreeToTerms: false
  });
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [showPasswordStrength, setShowPasswordStrength] = useState(false);
  
  const router = useRouter();
  const { signUp, user } = useAuth();

  // Redirect if already logged in
  useEffect(() => {
    if (user) {
      router.push('/courses/dashboard');
    }
  }, [user, router]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value, type, checked } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));
    
    // Clear error when user starts typing
    if (error) setError('');
    
    // Show password strength when user starts typing password
    if (name === 'password') {
      setShowPasswordStrength(value.length > 0);
    }
  };

  const validateForm = () => {
    if (!formData.name.trim()) {
      setError('Please enter your full name');
      return false;
    }

    if (!formData.email) {
      setError('Please enter your email address');
      return false;
    }

    if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      setError('Please enter a valid email address');
      return false;
    }

    if (formData.password.length < 8) {
      setError('Password must be at least 8 characters long');
      return false;
    }

    if (formData.password !== formData.confirmPassword) {
      setError('Passwords do not match');
      return false;
    }

    if (!formData.agreeToTerms) {
      setError('Please agree to the Terms of Service and Privacy Policy');
      return false;
    }

    return true;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError('');

    if (!validateForm()) {
      setIsLoading(false);
      return;
    }

    try {
      const result = await signUp(formData.email, formData.password, formData.name);

      if (result.success) {
        setSuccess('Account created successfully! Redirecting to onboarding...');
        
        // Redirect to onboarding after a brief delay
        setTimeout(() => {
          router.push('/onboarding');
        }, 1500);
      } else {
        setError(result.error || 'Failed to create account');
      }
    } catch (error: any) {
      console.error('Registration error:', error);
      setError(error.message || 'An unexpected error occurred');
    } finally {
      setIsLoading(false);
    }
  };

  const handleSocialSuccess = () => {
    setSuccess('Account created successfully! Redirecting...');
    setTimeout(() => {
      router.push('/onboarding');
    }, 1000);
  };

  const handleSocialError = (error: string) => {
    setError(error);
  };

  return (
    <AuthLayout
      title="Join InnoHub"
      subtitle="Create your account and start your learning journey"
      backHref="/courses/welcome"
      backText="Back to Courses"
    >
      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Alert Messages */}
        <AuthAlert
          type="error"
          message={error}
          show={!!error}
          onClose={() => setError('')}
        />
        
        <AuthAlert
          type="success"
          message={success}
          show={!!success}
          dismissible={false}
        />

        {/* Name Input */}
        <AuthInput
          label="Full Name"
          name="name"
          type="text"
          icon={User}
          placeholder="Enter your full name"
          value={formData.name}
          onChange={handleInputChange}
          required
          autoComplete="name"
        />

        {/* Email Input */}
        <AuthInput
          label="Email Address"
          name="email"
          type="email"
          icon={Mail}
          placeholder="Enter your email"
          value={formData.email}
          onChange={handleInputChange}
          required
          autoComplete="email"
        />

        {/* Password Input */}
        <div>
          <AuthInput
            label="Password"
            name="password"
            type="password"
            icon={Lock}
            placeholder="Create a strong password"
            value={formData.password}
            onChange={handleInputChange}
            showPasswordToggle
            required
            autoComplete="new-password"
          />
          
          <PasswordStrength
            password={formData.password}
            show={showPasswordStrength}
          />
        </div>

        {/* Confirm Password Input */}
        <AuthInput
          label="Confirm Password"
          name="confirmPassword"
          type="password"
          icon={Lock}
          placeholder="Confirm your password"
          value={formData.confirmPassword}
          onChange={handleInputChange}
          showPasswordToggle
          required
          autoComplete="new-password"
          error={formData.confirmPassword && formData.password !== formData.confirmPassword ? 'Passwords do not match' : undefined}
        />

        {/* Terms Agreement */}
        <div className="flex items-start">
          <input
            type="checkbox"
            name="agreeToTerms"
            checked={formData.agreeToTerms}
            onChange={handleInputChange}
            className="w-4 h-4 mt-1 text-purple-600 bg-gray-900 border-gray-600 rounded focus:ring-purple-500 focus:ring-2"
            required
          />
          <label className="ml-3 text-sm text-gray-400">
            I agree to the{' '}
            <Link href="/terms" className="text-purple-400 hover:text-purple-300">
              Terms of Service
            </Link>
            {' '}and{' '}
            <Link href="/privacy" className="text-purple-400 hover:text-purple-300">
              Privacy Policy
            </Link>
          </label>
        </div>

        {/* Submit Button */}
        <AuthButton
          type="submit"
          loading={isLoading}
          icon={UserPlus}
          className="w-full"
          disabled={!formData.name || !formData.email || !formData.password || !formData.confirmPassword || !formData.agreeToTerms}
        >
          {isLoading ? 'Creating Account...' : 'Create Account'}
        </AuthButton>

        {/* Social Authentication */}
        <SocialAuth
          mode="signup"
          onSuccess={handleSocialSuccess}
          onError={handleSocialError}
        />

        {/* Sign In Link */}
        <div className="text-center">
          <p className="text-gray-400">
            Already have an account?{' '}
            <Link
              href="/courses/auth/login"
              className="text-purple-400 hover:text-purple-300 font-medium transition-colors"
            >
              Sign in
            </Link>
          </p>
        </div>
      </form>
    </AuthLayout>
  );
}
