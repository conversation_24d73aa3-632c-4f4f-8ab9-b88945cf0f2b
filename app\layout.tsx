import type { <PERSON>ada<PERSON> } from "next";
import "./globals.css";
import "./timeline-effects.css";
import ClientLayout from "./client-layout";

import { AuthProvider } from "@/contexts/AuthContext";
import { TwentyFirstToolbar } from "@21st-extension/toolbar-next";
import { ReactPlugin } from "@21st-extension/react";

export const metadata: Metadata = {
  title: "InnoHub - Innovation Hub for Startups",
  description: "Accelerating the future of innovation with cutting-edge resources and mentorship",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <AuthProvider>
      <ClientLayout>
        {children}
        <TwentyFirstToolbar config={{ plugins: [ReactPlugin] }} />
      </ClientLayout>
    </AuthProvider>
  );
}
