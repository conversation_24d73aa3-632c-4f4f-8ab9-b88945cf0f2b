"use client";
import {
  useScroll,
  useTransform,
  motion,
} from "framer-motion";
import React, { useEffect, useRef, useState } from "react";
import TimelineImageEffect from "./timeline-image-effect";
import { useMobile, useMobilePerformance } from "@/hooks/use-mobile";

interface TimelineEntry {
  title: string;
  content: React.ReactNode;
}

export const Timeline = ({ data }: { data: TimelineEntry[] }) => {
  const ref = useRef<HTMLDivElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const [height, setHeight] = useState(0);
  const { isMobile } = useMobile();
  const { shouldReduceMotion } = useMobilePerformance();

  useEffect(() => {
    if (ref.current) {
      const rect = ref.current.getBoundingClientRect();
      setHeight(rect.height);
    }
  }, [ref]);

  const { scrollYProgress } = useScroll({
    target: containerRef,
    offset: ["start 10%", "end 50%"],
  });

  const heightTransform = useTransform(scrollYProgress, [0, 1], [0, height]);
  const opacityTransform = useTransform(scrollYProgress, [0, 0.1], [0, 1]);

  return (
    <div
      className="w-full bg-transparent dark:bg-transparent font-sans px-4 sm:px-6 md:px-10"
      ref={containerRef}
    >
      {!isMobile && <TimelineImageEffect />}
      <div className="max-w-7xl mx-auto py-12 sm:py-16 md:py-20 px-4 sm:px-6 md:px-8 lg:px-10">
        {/* Heading and description removed as requested */}
      </div>

      <div ref={ref} className="relative max-w-7xl mx-auto pb-12 sm:pb-16 md:pb-20">
        {data.map((item, index) => (
          <div
            key={`timeline-item-${item.title}-${index}`}
            className="flex justify-start pt-8 sm:pt-12 md:pt-16 lg:pt-48 md:gap-10"
          >
            <div className="sticky flex flex-col md:flex-row z-40 items-center top-20 sm:top-32 md:top-40 self-start max-w-xs lg:max-w-sm md:w-full">
              <div className="h-8 sm:h-10 absolute left-2 sm:left-3 md:left-3 w-8 sm:w-10 rounded-full bg-black dark:bg-black flex items-center justify-center touch-manipulation">
                <div className="h-3 w-3 sm:h-4 sm:w-4 rounded-full bg-purple-500 dark:bg-purple-500 border border-purple-300 dark:border-purple-700 p-1 sm:p-2" />
              </div>
              <h3 className="hidden md:block text-xl md:pl-20 lg:text-3xl xl:text-5xl font-bold text-purple-500 dark:text-purple-500">
                {item.title}
              </h3>
            </div>

            <div className="relative pl-12 sm:pl-16 md:pl-20 lg:pl-4 pr-4 w-full max-w-5xl">
              <h3 className="md:hidden block text-xl sm:text-2xl mb-3 sm:mb-4 text-left font-bold text-purple-500 dark:text-purple-500">
                {item.title}
              </h3>
              <div className="text-sm sm:text-base md:text-base">
                {item.content}
              </div>
            </div>
          </div>
        ))}
        <div
          style={{
            height: height + "px",
          }}
          className="absolute left-6 sm:left-7 md:left-8 top-0 overflow-hidden w-[2px] bg-[linear-gradient(to_bottom,var(--tw-gradient-stops))] from-transparent from-[0%] via-neutral-200 dark:via-neutral-700 to-transparent to-[99%] [mask-image:linear-gradient(to_bottom,transparent_0%,black_10%,black_90%,transparent_100%)]"
        >
          <motion.div
            style={{
              height: shouldReduceMotion ? height : heightTransform,
              opacity: shouldReduceMotion ? 1 : opacityTransform,
            }}
            className="absolute inset-x-0 top-0 w-[2px] bg-gradient-to-t from-purple-500 via-blue-500 to-transparent from-[0%] via-[10%] rounded-full"
          />
        </div>
      </div>
    </div>
  );
};
