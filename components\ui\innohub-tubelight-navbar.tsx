"use client"

import React, { useEffect, useState } from "react"
import { motion } from "framer-motion"
import Link from "next/link"
import Image from "next/image"
import { usePathname } from "next/navigation"
import { LucideIcon, Home, Briefcase, TrendingUp, Users, Newspaper, Globe, Menu, Info, GraduationCap } from "lucide-react"
import { cn } from "@/lib/utils"
import { useLanguage } from "@/lib/context/language-context"
import { Sheet, SheetContent, SheetTrigger } from '@/components/ui/sheet'
import { HoverBorderGradient } from '@/components/ui/hover-border-gradient'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu'

interface NavItem {
  name: string
  url: string
  icon: LucideIcon
  key: string
}

interface InnoHubTubeLightNavBarProps {
  className?: string
}

const navItems: NavItem[] = [
  { name: 'Home', url: '/', icon: Home, key: 'nav.home' },
  { name: 'About', url: '/about', icon: Info, key: 'nav.about' },
  { name: 'Programs', url: '/programs', icon: Briefcase, key: 'nav.programs' },
  { name: 'Success Stories', url: '/success-stories', icon: TrendingUp, key: 'nav.successStories' },
  { name: 'Investors', url: '/investors', icon: Users, key: 'nav.investors' },
  { name: 'News', url: '/news', icon: Newspaper, key: 'nav.news' },
]

export function InnoHubTubeLightNavBar({ className }: InnoHubTubeLightNavBarProps) {
  const [scrolled, setScrolled] = useState(false)
  const [isMobile, setIsMobile] = useState(false)
  const pathname = usePathname()
  const { t, language, setLanguage, isLoaded } = useLanguage()

  // Find active tab based on current pathname
  const activeItem = navItems.find(item => item.url === pathname) || navItems[0]

  useEffect(() => {
    const handleResize = () => {
      setIsMobile(window.innerWidth < 768)
    }

    const handleScroll = () => {
      const isScrolled = window.scrollY > 10
      if (isScrolled !== scrolled) {
        setScrolled(isScrolled)
      }
    }

    handleResize()
    handleScroll()
    window.addEventListener("resize", handleResize)
    window.addEventListener('scroll', handleScroll)
    
    return () => {
      window.removeEventListener("resize", handleResize)
      window.removeEventListener('scroll', handleScroll)
    }
  }, [scrolled])

  return (
    <header
      className={cn(
        "fixed top-0 left-0 right-0 z-50 transition-all duration-300",
        scrolled ? 'bg-black/80 backdrop-blur-md border-b border-purple-500' : 'bg-transparent',
        className,
      )}
    >
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4 flex items-center justify-between">
        {/* Logo (Left) */}
        <Link href="/" className="flex items-center gap-2 z-10">
          <div className="relative w-10 h-10 rounded-full overflow-hidden">
            <Image
              src="/images/logo/innohub_logo.png"
              alt="InnoHub Logo"
              width={40}
              height={40}
              className="object-cover"
            />
          </div>
          <span className="font-bold text-xl text-white">INNO<span className="text-purple-500">HUB</span></span>
        </Link>

        {/* Desktop Tubelight Navigation (Center) */}
        <nav className="hidden md:flex items-center absolute left-1/2 transform -translate-x-1/2">
          <div className="flex items-center gap-1 bg-black/20 border border-purple-500/30 backdrop-blur-lg py-1 px-1 rounded-full shadow-lg">
            {navItems.map((item) => {
              const Icon = item.icon
              const isActive = pathname === item.url

              return (
                <Link
                  key={item.url}
                  href={item.url}
                  className={cn(
                    "relative cursor-pointer text-sm font-semibold px-4 py-2 rounded-full transition-all duration-300",
                    "text-white/80 hover:text-white",
                    isActive && "text-white",
                  )}
                >
                  <span className="relative z-10">{isLoaded ? t(item.key) : item.name}</span>
                  {isActive && (
                    <motion.div
                      layoutId="tubelight"
                      className="absolute inset-0 w-full bg-purple-600/20 rounded-full -z-10"
                      initial={false}
                      transition={{
                        type: "spring",
                        stiffness: 300,
                        damping: 30,
                      }}
                    >
                      {/* Tubelight effect */}
                      <div className="absolute -top-2 left-1/2 -translate-x-1/2 w-8 h-1 bg-purple-500 rounded-t-full">
                        <div className="absolute w-12 h-6 bg-purple-500/30 rounded-full blur-md -top-2 -left-2" />
                        <div className="absolute w-8 h-6 bg-purple-500/40 rounded-full blur-md -top-1" />
                        <div className="absolute w-4 h-4 bg-purple-500/50 rounded-full blur-sm top-0 left-2" />
                      </div>
                    </motion.div>
                  )}
                </Link>
              )
            })}
          </div>
        </nav>

        {/* Right Side Actions */}
        <div className="hidden md:flex items-center gap-4 z-10">
          {/* Courses Button */}
          <Link href="/courses">
            <HoverBorderGradient
              containerClassName="rounded-full"
              className="dark:bg-slate-950 bg-slate-950 text-white px-4 py-1.5 text-sm"
              duration={2}
            >
              {isLoaded ? t('nav.courses') : 'Courses'}
            </HoverBorderGradient>
          </Link>

          {/* Language Switcher */}
          {isLoaded && (
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <HoverBorderGradient
                  containerClassName="rounded-full h-[2.25rem] w-[3rem]"
                  className="dark:bg-slate-950 bg-slate-950 text-white flex items-center justify-center gap-1 h-full w-full px-2"
                  duration={2}
                >
                  <Globe className="h-4 w-4" />
                  <span className="text-sm">{language === 'en' ? '🇺🇸' : '🇲🇳'}</span>
                  <span className="sr-only">{t('nav.language')}</span>
                </HoverBorderGradient>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="bg-black/90 backdrop-blur-md border border-purple-500 rounded-xl">
                <DropdownMenuItem
                  className={`${language === 'en' ? 'bg-purple-700/30' : ''} text-white hover:bg-black/70 rounded-lg my-1 flex items-center gap-2`}
                  onClick={() => setLanguage('en')}
                >
                  <span>🇺🇸</span>
                  {t('nav.english')}
                </DropdownMenuItem>
                <DropdownMenuItem
                  className={`${language === 'mn' ? 'bg-purple-700/30' : ''} text-white hover:bg-black/70 rounded-lg my-1 flex items-center gap-2`}
                  onClick={() => setLanguage('mn')}
                >
                  <span>🇲🇳</span>
                  {t('nav.mongolian')}
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          )}
        </div>

        {/* Mobile Navigation - Enhanced for Touch */}
        <Sheet>
          <SheetTrigger asChild className="md:hidden">
            <motion.button
              className="relative flex items-center justify-center w-14 h-14 rounded-full bg-black/50 backdrop-blur-md border border-purple-500/40 text-white shadow-lg shadow-purple-500/20 active:bg-black/70"
              whileTap={{ scale: 0.92 }}
              whileHover={{ scale: 1.05 }}
              transition={{ duration: 0.2 }}
              style={{ minHeight: '56px', minWidth: '56px' }}
            >
              <Menu className="h-7 w-7" />
              <span className="sr-only">Toggle menu</span>
            </motion.button>
          </SheetTrigger>
          <SheetContent
            side="right"
            className="w-full sm:w-[400px] bg-black/95 backdrop-blur-xl border-purple-500/50 p-0 overflow-y-auto"
          >
            <div className="flex flex-col h-full min-h-screen">
              {/* Header */}
              <div className="flex items-center justify-between p-6 border-b border-purple-500/20 sticky top-0 bg-black/95 backdrop-blur-xl z-10">
                <div className="flex items-center gap-3">
                  <div className="relative w-10 h-10 rounded-full overflow-hidden">
                    <Image
                      src="/images/logo/innohub_logo.png"
                      alt="InnoHub Logo"
                      width={40}
                      height={40}
                      className="object-cover"
                    />
                  </div>
                  <span className="font-bold text-xl text-white">INNO<span className="text-purple-500">HUB</span></span>
                </div>
              </div>

              {/* Navigation Links */}
              <nav className="flex-1 px-6 py-4">
                <div className="space-y-2">
                  {navItems.map((item, index) => {
                    const Icon = item.icon
                    const isActive = pathname === item.url

                    return (
                      <motion.div
                        key={item.url}
                        initial={{ opacity: 0, x: 20 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ duration: 0.3, delay: index * 0.1 }}
                      >
                        <Link
                          href={item.url}
                          className={cn(
                            "flex items-center gap-4 p-5 rounded-xl transition-all duration-300 min-h-[64px]",
                            "text-white/80 hover:text-white hover:bg-purple-500/15",
                            "active:scale-95 active:bg-purple-500/25 touch-manipulation",
                            "focus:outline-none focus:ring-2 focus:ring-purple-500/50",
                            isActive && "bg-purple-500/20 text-white border border-purple-500/30"
                          )}
                        >
                          <Icon className="h-5 w-5 flex-shrink-0" />
                          <span className="text-base font-medium">
                            {isLoaded ? t(item.key) : item.name}
                          </span>
                          {isActive && (
                            <motion.div
                              className="ml-auto w-2 h-2 bg-purple-500 rounded-full"
                              initial={{ scale: 0 }}
                              animate={{ scale: 1 }}
                              transition={{ duration: 0.3 }}
                            />
                          )}
                        </Link>
                      </motion.div>
                    )
                  })}

                  {/* Courses Link */}
                  <motion.div
                    initial={{ opacity: 0, x: 20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ duration: 0.3, delay: navItems.length * 0.1 }}
                  >
                    <Link
                      href="/courses"
                      className="flex items-center gap-4 p-4 rounded-xl transition-all duration-300 min-h-[56px] text-white/80 hover:text-white hover:bg-purple-500/10 active:scale-95 active:bg-purple-500/20"
                    >
                      <GraduationCap className="h-5 w-5 flex-shrink-0" />
                      <span className="text-base font-medium">
                        {isLoaded ? t('nav.courses') : 'Courses'}
                      </span>
                    </Link>
                  </motion.div>
                </div>
              </nav>

              {/* Language Switcher */}
              {isLoaded && (
                <div className="p-6 border-t border-purple-500/20">
                  <p className="text-white/60 text-sm mb-3">{t('nav.language')}:</p>
                  <div className="flex gap-3">
                    <motion.button
                      onClick={() => setLanguage('en')}
                      className={cn(
                        "flex items-center gap-2 px-4 py-3 rounded-xl transition-all duration-300 min-h-[48px] flex-1",
                        language === 'en'
                          ? 'bg-purple-500/30 text-white border border-purple-500/50'
                          : 'bg-white/5 text-white/80 hover:bg-white/10'
                      )}
                      whileTap={{ scale: 0.95 }}
                    >
                      <span className="text-base">🇺🇸</span>
                      <span className="text-sm font-medium">{t('nav.english')}</span>
                    </motion.button>
                    <motion.button
                      onClick={() => setLanguage('mn')}
                      className={cn(
                        "flex items-center gap-2 px-4 py-3 rounded-xl transition-all duration-300 min-h-[48px] flex-1",
                        language === 'mn'
                          ? 'bg-purple-500/30 text-white border border-purple-500/50'
                          : 'bg-white/5 text-white/80 hover:bg-white/10'
                      )}
                      whileTap={{ scale: 0.95 }}
                    >
                      <span className="text-base">🇲🇳</span>
                      <span className="text-sm font-medium">{t('nav.mongolian')}</span>
                    </motion.button>
                  </div>
                </div>
              )}
            </div>
          </SheetContent>
        </Sheet>
      </div>
    </header>
  )
}
