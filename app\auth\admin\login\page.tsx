'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
import { motion } from 'framer-motion';
import Link from 'next/link';
import { Mail, Lock, Shield, AlertTriangle } from 'lucide-react';

import AuthLayout from '@/components/auth/AuthLayout';
import AuthInput from '@/components/auth/AuthInput';
import AuthButton from '@/components/auth/AuthButton';
import AuthAlert from '@/components/auth/AuthAlert';

export default function AdminLoginPage() {
  const [formData, setFormData] = useState({
    email: '',
    password: ''
  });
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  
  const router = useRouter();
  const { user } = useAuth();

  // Redirect if already logged in as admin
  useEffect(() => {
    if (user && user.role === 'ADMIN') {
      router.push('/admin');
    }
  }, [user, router]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
    
    // Clear error when user starts typing
    if (error) setError('');
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError('');

    // Basic validation
    if (!formData.email || !formData.password) {
      setError('Please fill in all fields');
      setIsLoading(false);
      return;
    }

    try {
      // Use the admin login API endpoint
      const response = await fetch('/api/auth/admin-login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email: formData.email,
          password: formData.password
        }),
      });

      const result = await response.json();

      if (result.success) {
        setSuccess('Admin login successful! Redirecting...');
        
        // Set admin session in localStorage
        localStorage.setItem('innohub_admin', 'true');
        
        // Set admin cookie
        document.cookie = 'innohub_admin=true; path=/; max-age=86400'; // 24 hours
        
        // Redirect after a brief delay
        setTimeout(() => {
          router.push('/admin');
        }, 1000);
      } else {
        setError(result.error || 'Invalid admin credentials');
      }
    } catch (error: any) {
      console.error('Admin login error:', error);
      setError('An unexpected error occurred. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <AuthLayout
      title="Admin Access"
      subtitle="Secure login for InnoHub administrators"
      backHref="/"
      backText="Back to Home"
    >
      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Security Notice */}
        <motion.div
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-yellow-900/20 border border-yellow-500/30 rounded-lg p-4 mb-6"
        >
          <div className="flex items-start">
            <AlertTriangle className="h-5 w-5 text-yellow-400 mt-0.5 mr-3 flex-shrink-0" />
            <div>
              <h4 className="text-yellow-300 font-medium mb-1">Restricted Access</h4>
              <p className="text-yellow-200/80 text-sm">
                This area is restricted to authorized administrators only. 
                All login attempts are monitored and logged.
              </p>
            </div>
          </div>
        </motion.div>

        {/* Alert Messages */}
        <AuthAlert
          type="error"
          message={error}
          show={!!error}
          onClose={() => setError('')}
        />
        
        <AuthAlert
          type="success"
          message={success}
          show={!!success}
          dismissible={false}
        />

        {/* Email Input */}
        <AuthInput
          label="Admin Email"
          name="email"
          type="email"
          icon={Mail}
          placeholder="Enter your admin email"
          value={formData.email}
          onChange={handleInputChange}
          required
          autoComplete="email"
        />

        {/* Password Input */}
        <AuthInput
          label="Admin Password"
          name="password"
          type="password"
          icon={Lock}
          placeholder="Enter your admin password"
          value={formData.password}
          onChange={handleInputChange}
          showPasswordToggle
          required
          autoComplete="current-password"
        />

        {/* Submit Button */}
        <AuthButton
          type="submit"
          loading={isLoading}
          icon={Shield}
          className="w-full"
          disabled={!formData.email || !formData.password}
        >
          {isLoading ? 'Verifying Access...' : 'Access Admin Panel'}
        </AuthButton>

        {/* Admin Registration Link */}
        <div className="text-center">
          <p className="text-gray-400 text-sm">
            Need admin access?{' '}
            <Link
              href="/auth/admin/register"
              className="text-purple-400 hover:text-purple-300 font-medium transition-colors"
            >
              Request Admin Account
            </Link>
          </p>
        </div>

        {/* Security Footer */}
        <div className="text-center pt-4 border-t border-gray-700">
          <p className="text-xs text-gray-500">
            Protected by enterprise-grade security
          </p>
        </div>
      </form>
    </AuthLayout>
  );
}
