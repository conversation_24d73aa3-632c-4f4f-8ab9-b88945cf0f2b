-- Fix infinite recursion in RLS policies
-- Drop the problematic policies first
DROP POLICY IF EXISTS "Ad<PERSON> can view all users" ON public.users;
DROP POLICY IF EXISTS "Ad<PERSON> can update all users" ON public.users;
DROP POLICY IF EXISTS "Ad<PERSON> can view all enrollments" ON public.enrollments;
DROP POLICY IF EXISTS "Instructors can view progress" ON public.lesson_progress;
DROP POLICY IF EXISTS "Instructors can manage blog posts" ON public.blog_posts;
DROP POLICY IF EXISTS "Ad<PERSON> can view all payments" ON public.payments;

-- Allow service role to manage users (for admin operations)
CREATE POLICY "Service role can manage users" ON public.users FOR ALL USING (auth.role() = 'service_role');

-- Also need to add INSERT policy for users (for registration)
CREATE POLICY "Anyone can insert users" ON public.users FOR INSERT WITH CHECK (true);

-- For other tables, create simpler policies without recursion
CREATE POLICY "<PERSON><PERSON> can view all enrollments" ON public.enrollments FOR SELECT USING (
    auth.role() = 'service_role'
);

CREATE POLICY "Instructors can view progress" ON public.lesson_progress FOR SELECT USING (
    auth.role() = 'service_role'
);

CREATE POLICY "Instructors can manage blog posts" ON public.blog_posts FOR ALL USING (
    auth.role() = 'service_role'
);

CREATE POLICY "Admins can view all payments" ON public.payments FOR SELECT USING (
    auth.role() = 'service_role'
);