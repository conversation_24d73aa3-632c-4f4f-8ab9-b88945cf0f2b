'use client';

import { useRouter, useSearchParams } from 'next/navigation';
import { motion } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { XCircle, ArrowLeft, RefreshCw, Home } from 'lucide-react';
import Link from 'next/link';

export default function PaymentCancelPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const orderId = searchParams.get('orderId');

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-900 via-black to-purple-900 flex items-center justify-center p-4">
      <motion.div
        initial={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ duration: 0.5 }}
        className="w-full max-w-md"
      >
        <Card className="border-red-500/20 bg-black/60 backdrop-blur-md">
          <CardHeader className="text-center space-y-4">
            <motion.div
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              transition={{ delay: 0.2, type: "spring", stiffness: 200 }}
              className="mx-auto w-16 h-16 bg-red-500/20 rounded-full flex items-center justify-center"
            >
              <XCircle className="h-8 w-8 text-red-400" />
            </motion.div>
            <div>
              <CardTitle className="text-2xl font-bold text-white">
                Төлбөр цуцлагдлаа
              </CardTitle>
              <p className="text-gray-400 mt-2">
                Таны төлбөрийн үйл явц цуцлагдлаа
              </p>
            </div>
          </CardHeader>

          <CardContent className="space-y-6">
            {/* Cancel Info */}
            <div className="bg-red-500/10 border border-red-500/20 rounded-lg p-4 space-y-3">
              <div className="text-center">
                <h3 className="text-red-400 font-medium mb-2">
                  Төлбөр хийгдээгүй
                </h3>
                <p className="text-gray-400 text-sm">
                  Таны төлбөрийн үйл явц цуцлагдсан эсвэл таслагдсан байна.
                  Хэрэв энэ нь алдаа бол дахин оролдоно уу.
                </p>
              </div>
              
              {orderId && (
                <div className="border-t border-red-500/20 pt-3">
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-400">Захиалгын дугаар:</span>
                    <span className="text-white font-mono">{orderId}</span>
                  </div>
                </div>
              )}
            </div>

            {/* Possible Reasons */}
            <div className="bg-yellow-500/10 border border-yellow-500/20 rounded-lg p-4">
              <h4 className="text-yellow-400 font-medium mb-2">
                Боломжит шалтгаанууд:
              </h4>
              <ul className="text-gray-400 text-sm space-y-1">
                <li>• Төлбөрийн цонхыг хаасан</li>
                <li>• Интернэт холболт тасарсан</li>
                <li>• Банкны картын мэдээлэл буруу</li>
                <li>• Хангалттай үлдэгдэл байхгүй</li>
              </ul>
            </div>

            {/* Action Buttons */}
            <div className="space-y-3">
              <Button
                onClick={() => router.back()}
                className="w-full bg-purple-600 hover:bg-purple-700 text-white"
              >
                <RefreshCw className="h-4 w-4 mr-2" />
                Дахин оролдох
              </Button>
              
              <Button
                onClick={() => router.push('/courses')}
                variant="outline"
                className="w-full border-gray-600 text-gray-300 hover:bg-gray-800"
              >
                <ArrowLeft className="h-4 w-4 mr-2" />
                Хичээлүүд рүү буцах
              </Button>
              
              <Link href="/" className="block">
                <Button
                  variant="outline"
                  className="w-full border-gray-600 text-gray-300 hover:bg-gray-800"
                >
                  <Home className="h-4 w-4 mr-2" />
                  Нүүр хуудас
                </Button>
              </Link>
            </div>

            {/* Support Info */}
            <div className="bg-blue-500/10 border border-blue-500/20 rounded-lg p-3">
              <p className="text-blue-400 text-sm text-center">
                💬 Тусламж хэрэгтэй бол бидэнтэй холбогдоно уу:<br />
                <span className="font-mono"><EMAIL></span>
              </p>
            </div>
          </CardContent>
        </Card>
      </motion.div>
    </div>
  );
}
