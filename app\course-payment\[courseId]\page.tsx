'use client';

import { useState } from 'react';
import { usePara<PERSON>, useRouter } from 'next/navigation';
import { motion } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { ArrowLeft, Clock, Users, Star, BookOpen, Smartphone, ArrowRight } from 'lucide-react';
import Image from 'next/image';
import QPayPayment from '@/components/payment/MongolianPayment';

// Convert real courses to payment format
const courseMap = realCourses.reduce((acc, course) => {
  acc[course.id] = {
    id: course.id,
    title: course.titleMn,
    description: course.descriptionMn,
    price: course.price,
    originalPrice: course.originalPrice,
    duration: course.durationMn,
    students: course.enrollmentCount,
    rating: course.rating,
    instructor: course.instructorMn,
    image: course.thumbnail,
    features: course.learningOutcomesMn
  };
  return acc;
}, {} as Record<string, any>);

export default function CoursePaymentPage() {
  const params = useParams();
  const router = useRouter();
  const courseId = params.courseId as string;
  const [showPayment, setShowPayment] = useState(false);

  const course = courseMap[courseId];

  if (!course) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-purple-900 via-black to-purple-900 flex items-center justify-center p-4">
        <Card className="border-red-500/20 bg-black/60 backdrop-blur-md">
          <CardContent className="p-8 text-center">
            <h1 className="text-white text-xl mb-4">Хичээл олдсонгүй</h1>
            <Button onClick={() => router.push('/courses')} variant="outline">
              Хичээлүүд рүү буцах
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('mn-MN', {
      style: 'currency',
      currency: 'MNT',
      minimumFractionDigits: 0
    }).format(price);
  };

  const discountPercentage = Math.round(((course.originalPrice - course.price) / course.originalPrice) * 100);

  const handlePaymentSuccess = (paymentData: any) => {
    console.log('Payment successful:', paymentData);
    router.push('/payment/success?orderId=' + paymentData.orderId);
  };

  if (showPayment) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-purple-900 via-black to-purple-900 flex items-center justify-center p-4">
        <div className="w-full max-w-md">
          <QPayPayment
            courseId={course.id}
            courseName={course.title}
            amount={course.price}
            onSuccess={handlePaymentSuccess}
            onCancel={() => setShowPayment(false)}
          />
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-900 via-black to-purple-900 p-4">
      <div className="max-w-4xl mx-auto">
        {/* Back Button */}
        <Button
          onClick={() => router.back()}
          variant="outline"
          className="mb-6 border-gray-600 text-gray-300 hover:bg-gray-800"
        >
          <ArrowLeft className="h-4 w-4 mr-2" />
          Буцах
        </Button>

        <div className="grid lg:grid-cols-2 gap-8">
          {/* Course Info */}
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.5 }}
          >
            <Card className="border-purple-500/20 bg-black/60 backdrop-blur-md">
              <CardHeader>
                <div className="relative h-48 rounded-lg overflow-hidden mb-4">
                  <Image
                    src={course.image}
                    alt={course.title}
                    fill
                    className="object-cover"
                    onError={(e) => {
                      e.currentTarget.src = '/images/courses/default.jpg';
                    }}
                  />
                </div>
                <CardTitle className="text-white text-2xl">{course.title}</CardTitle>
                <p className="text-gray-400">{course.description}</p>
              </CardHeader>

              <CardContent className="space-y-6">
                {/* Course Stats */}
                <div className="grid grid-cols-2 gap-4">
                  <div className="flex items-center gap-2 text-gray-300">
                    <Clock className="h-4 w-4" />
                    <span className="text-sm">{course.duration}</span>
                  </div>
                  <div className="flex items-center gap-2 text-gray-300">
                    <Users className="h-4 w-4" />
                    <span className="text-sm">{course.students} оюутан</span>
                  </div>
                  <div className="flex items-center gap-2 text-gray-300">
                    <Star className="h-4 w-4 text-yellow-400" />
                    <span className="text-sm">{course.rating} үнэлгээ</span>
                  </div>
                  <div className="flex items-center gap-2 text-gray-300">
                    <BookOpen className="h-4 w-4" />
                    <span className="text-sm">{course.instructor}</span>
                  </div>
                </div>

                {/* Features */}
                <div>
                  <h3 className="text-white font-medium mb-3">Та юу сурах вэ:</h3>
                  <ul className="space-y-2">
                    {course.features.map((feature, index) => (
                      <li key={index} className="flex items-center gap-2 text-gray-300">
                        <div className="w-2 h-2 bg-purple-500 rounded-full" />
                        <span className="text-sm">{feature}</span>
                      </li>
                    ))}
                  </ul>
                </div>
              </CardContent>
            </Card>
          </motion.div>

          {/* Payment Card */}
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.5, delay: 0.2 }}
          >
            <Card className="border-purple-500/20 bg-black/60 backdrop-blur-md sticky top-4">
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle className="text-white">Хичээл авах</CardTitle>
                  <Badge className="bg-blue-500/20 text-blue-400 border-blue-500/30">
                    QPay дэмжигдсэн
                  </Badge>
                </div>
              </CardHeader>

              <CardContent className="space-y-6">
                {/* Pricing */}
                <div className="text-center space-y-2">
                  <div className="flex items-center justify-center gap-3">
                    <span className="text-3xl font-bold text-white">
                      {formatPrice(course.price)}
                    </span>
                    {course.originalPrice > course.price && (
                      <Badge className="bg-red-500/20 text-red-400">
                        -{discountPercentage}%
                      </Badge>
                    )}
                  </div>
                  
                  {course.originalPrice > course.price && (
                    <p className="text-gray-400 line-through">
                      {formatPrice(course.originalPrice)}
                    </p>
                  )}
                  
                  <p className="text-green-400 text-sm font-medium">
                    🎉 Хямдралтай үнэ!
                  </p>
                </div>

                {/* What's Included */}
                <div className="bg-purple-500/10 border border-purple-500/20 rounded-lg p-4">
                  <h4 className="text-white font-medium mb-3">Багтсан зүйлс:</h4>
                  <ul className="space-y-2 text-sm text-gray-300">
                    <li>✅ Бүх видео хичээлүүд</li>
                    <li>✅ Даалгавар болон шалгалтууд</li>
                    <li>✅ Гэрчилгээ</li>
                    <li>✅ Насан туршийн хандалт</li>
                    <li>✅ Багшийн дэмжлэг</li>
                  </ul>
                </div>

                {/* Purchase Button */}
                <Button
                  onClick={() => setShowPayment(true)}
                  className="w-full bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white font-medium py-4 text-lg rounded-xl shadow-lg hover:shadow-xl transition-all duration-300"
                >
                  <Smartphone className="h-5 w-5 mr-2" />
                  QPay-ээр төлөх
                  <ArrowRight className="h-5 w-5 ml-2" />
                </Button>

                {/* Money Back Guarantee */}
                <div className="text-center">
                  <p className="text-gray-400 text-sm">
                    💰 30 хоногийн мөнгө буцаах баталгаа
                  </p>
                </div>

                {/* Security */}
                <div className="text-center border-t border-gray-700 pt-4">
                  <p className="text-gray-500 text-xs">
                    🔒 Найдвартай төлбөрийн систем
                  </p>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        </div>
      </div>
    </div>
  );
}
