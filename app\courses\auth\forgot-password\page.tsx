'use client';

import { useState } from 'react';
import { motion } from 'framer-motion';
import Link from 'next/link';
import { Mail, ArrowLeft, Send } from 'lucide-react';

import AuthLayout from '@/components/auth/AuthLayout';
import AuthInput from '@/components/auth/AuthInput';
import AuthButton from '@/components/auth/AuthButton';
import AuthAlert from '@/components/auth/AuthAlert';
import { createSupabaseClient } from '@/lib/supabase/client';

export default function ForgotPasswordPage() {
  const [email, setEmail] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [emailSent, setEmailSent] = useState(false);

  const supabase = createSupabaseClient();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError('');

    if (!email) {
      setError('Please enter your email address');
      setIsLoading(false);
      return;
    }

    if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
      setError('Please enter a valid email address');
      setIsLoading(false);
      return;
    }

    try {
      const { error } = await supabase.auth.resetPasswordForEmail(email, {
        redirectTo: `${window.location.origin}/courses/auth/reset-password`
      });

      if (error) {
        setError(error.message);
      } else {
        setSuccess('Password reset email sent! Please check your inbox.');
        setEmailSent(true);
      }
    } catch (error: any) {
      console.error('Password reset error:', error);
      setError('An unexpected error occurred. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleResendEmail = async () => {
    setIsLoading(true);
    setError('');
    
    try {
      const { error } = await supabase.auth.resetPasswordForEmail(email, {
        redirectTo: `${window.location.origin}/courses/auth/reset-password`
      });

      if (error) {
        setError(error.message);
      } else {
        setSuccess('Password reset email sent again! Please check your inbox.');
      }
    } catch (error: any) {
      setError('Failed to resend email. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <AuthLayout
      title="Reset Password"
      subtitle="Enter your email to receive a password reset link"
      backHref="/courses/auth/login"
      backText="Back to Login"
    >
      {!emailSent ? (
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Alert Messages */}
          <AuthAlert
            type="error"
            message={error}
            show={!!error}
            onClose={() => setError('')}
          />

          {/* Instructions */}
          <div className="text-center text-gray-400 text-sm">
            <p>
              Enter the email address associated with your account and we'll send you 
              a link to reset your password.
            </p>
          </div>

          {/* Email Input */}
          <AuthInput
            label="Email Address"
            name="email"
            type="email"
            icon={Mail}
            placeholder="Enter your email"
            value={email}
            onChange={(e) => {
              setEmail(e.target.value);
              if (error) setError('');
            }}
            required
            autoComplete="email"
          />

          {/* Submit Button */}
          <AuthButton
            type="submit"
            loading={isLoading}
            icon={Send}
            className="w-full"
            disabled={!email}
          >
            {isLoading ? 'Sending Reset Link...' : 'Send Reset Link'}
          </AuthButton>

          {/* Back to Login */}
          <div className="text-center">
            <Link
              href="/courses/auth/login"
              className="inline-flex items-center text-gray-400 hover:text-white transition-colors"
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Login
            </Link>
          </div>
        </form>
      ) : (
        <div className="space-y-6 text-center">
          {/* Success Message */}
          <AuthAlert
            type="success"
            message={success}
            show={!!success}
            dismissible={false}
          />

          <AuthAlert
            type="error"
            message={error}
            show={!!error}
            onClose={() => setError('')}
          />

          {/* Email Sent Confirmation */}
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            className="bg-green-900/20 border border-green-500/30 rounded-lg p-6"
          >
            <div className="w-16 h-16 bg-green-500/20 rounded-full flex items-center justify-center mx-auto mb-4">
              <Mail className="w-8 h-8 text-green-400" />
            </div>
            <h3 className="text-lg font-medium text-white mb-2">Check Your Email</h3>
            <p className="text-gray-400 text-sm mb-4">
              We've sent a password reset link to <strong className="text-white">{email}</strong>
            </p>
            <p className="text-gray-500 text-xs">
              Didn't receive the email? Check your spam folder or try again.
            </p>
          </motion.div>

          {/* Action Buttons */}
          <div className="space-y-3">
            <AuthButton
              onClick={handleResendEmail}
              loading={isLoading}
              variant="outline"
              className="w-full"
            >
              {isLoading ? 'Resending...' : 'Resend Email'}
            </AuthButton>

            <Link
              href="/courses/auth/login"
              className="block w-full text-center text-gray-400 hover:text-white transition-colors"
            >
              Back to Login
            </Link>
          </div>
        </div>
      )}
    </AuthLayout>
  );
}
