import { NextRequest, NextResponse } from 'next/server';
import { createSupabaseServerClient } from '@/lib/supabase/client';

export async function GET(request: NextRequest) {
  try {
    const serverClient = createSupabaseServerClient();
    
    // Check admin access
    const { data: { session }, error: sessionError } = await serverClient.auth.getSession();
    if (sessionError || !session) {
      return NextResponse.json({ error: 'Not authenticated' }, { status: 401 });
    }

    const { data: user } = await serverClient
      .from('users')
      .select('role')
      .eq('id', session.user.id)
      .single();

    if (!user || user.role !== 'ADMIN') {
      return NextResponse.json({ error: 'Access denied' }, { status: 403 });
    }

    // Get total counts with error handling
    let totalUsers = 0, totalCourses = 0, totalBlogs = 0, totalEnrollments = 0;
    let recentUsers: any[] = [], recentCourses: any[] = [], recentBlogs: any[] = [];

    try {
      const usersResult = await serverClient.from('users').select('*', { count: 'exact', head: true });
      totalUsers = usersResult.count || 0;
    } catch (error) {
      console.error('Error counting users:', error);
    }

    try {
      const coursesResult = await serverClient.from('courses').select('*', { count: 'exact', head: true });
      totalCourses = coursesResult.count || 0;
    } catch (error) {
      console.error('Error counting courses:', error);
    }

    try {
      const blogsResult = await serverClient.from('blog_posts').select('*', { count: 'exact', head: true });
      totalBlogs = blogsResult.count || 0;
    } catch (error) {
      console.error('Error counting blogs:', error);
    }

    try {
      const enrollmentsResult = await serverClient.from('course_enrollments').select('*', { count: 'exact', head: true });
      totalEnrollments = enrollmentsResult.count || 0;
    } catch (error) {
      console.error('Error counting enrollments:', error);
    }

    // Get recent users
    try {
      const { data } = await serverClient
        .from('users')
        .select('id, name, email, role, created_at')
        .order('created_at', { ascending: false })
        .limit(5);
      recentUsers = data || [];
    } catch (error) {
      console.error('Error fetching recent users:', error);
    }

    // Get recent courses
    try {
      const { data } = await serverClient
        .from('courses')
        .select('id, title, instructor, is_published, created_at')
        .order('created_at', { ascending: false })
        .limit(5);
      recentCourses = data || [];
    } catch (error) {
      console.error('Error fetching recent courses:', error);
    }

    // Get recent blogs
    try {
      const { data } = await serverClient
        .from('blog_posts')
        .select('id, title, is_published, created_at')
        .order('created_at', { ascending: false })
        .limit(5);
      recentBlogs = data || [];
    } catch (error) {
      console.error('Error fetching recent blogs:', error);
    }

    return NextResponse.json({
      totalUsers,
      totalCourses,
      totalBlogs,
      totalEnrollments,
      recentUsers,
      recentCourses,
      recentBlogs
    });

  } catch (error: any) {
    console.error('Dashboard stats error:', error);
    return NextResponse.json(
      { error: 'Failed to load dashboard stats' },
      { status: 500 }
    );
  }
}
