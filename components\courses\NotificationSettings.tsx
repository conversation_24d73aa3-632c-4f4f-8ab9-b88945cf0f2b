'use client';

import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { Badge } from '@/components/ui/badge';
import { 
  Bell, 
  Mail, 
  BookOpen, 
  TrendingUp, 
  Gift, 
  Award,
  Save,
  CheckCircle
} from 'lucide-react';
import { useLanguage } from '@/lib/context/language-context';
import { useAuth } from '@/contexts/AuthContext';
import notificationService, { NotificationPreferences } from '@/lib/services/notificationService';

export default function NotificationSettings() {
  const { t, language } = useLanguage();
  const { user } = useAuth();
  const [preferences, setPreferences] = useState<NotificationPreferences>({
    emailNotifications: true,
    courseUpdates: true,
    progressReminders: true,
    promotionalEmails: true,
    certificateNotifications: true
  });
  const [isSaving, setIsSaving] = useState(false);
  const [saved, setSaved] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    loadPreferences();
  }, [user]);

  const loadPreferences = async () => {
    if (!user?.id) return;

    try {
      setIsLoading(true);
      const userPreferences = await notificationService.getUserPreferences(user.id);
      setPreferences(userPreferences);
    } catch (error) {
      console.error('Failed to load preferences:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handlePreferenceChange = (key: keyof NotificationPreferences, value: boolean) => {
    setPreferences(prev => ({
      ...prev,
      [key]: value
    }));
    setSaved(false);
  };

  const handleSave = async () => {
    if (!user?.id) return;

    setIsSaving(true);
    try {
      await notificationService.updateUserPreferences(user.id, preferences);
      setSaved(true);
      setTimeout(() => setSaved(false), 3000);
    } catch (error) {
      console.error('Failed to save preferences:', error);
    } finally {
      setIsSaving(false);
    }
  };

  const notificationTypes = [
    {
      key: 'emailNotifications' as keyof NotificationPreferences,
      icon: <Mail className="h-5 w-5" />,
      title: language === 'mn' ? 'И-мэйл мэдэгдэл' : 'Email Notifications',
      description: language === 'mn' 
        ? 'Бүх и-мэйл мэдэгдлийг идэвхжүүлэх/идэвхгүй болгох'
        : 'Enable or disable all email notifications',
      master: true
    },
    {
      key: 'courseUpdates' as keyof NotificationPreferences,
      icon: <BookOpen className="h-5 w-5" />,
      title: language === 'mn' ? 'Хичээлийн шинэчлэл' : 'Course Updates',
      description: language === 'mn'
        ? 'Хичээлийн бүртгэл, шинэ контент болон өөрчлөлтийн мэдэгдэл'
        : 'Notifications about course enrollment, new content, and updates'
    },
    {
      key: 'progressReminders' as keyof NotificationPreferences,
      icon: <TrendingUp className="h-5 w-5" />,
      title: language === 'mn' ? 'Явцын сануулга' : 'Progress Reminders',
      description: language === 'mn'
        ? 'Сурах явцыг үргэлжлүүлэхийг сануулах мэдэгдэл'
        : 'Reminders to continue your learning progress'
    },
    {
      key: 'certificateNotifications' as keyof NotificationPreferences,
      icon: <Award className="h-5 w-5" />,
      title: language === 'mn' ? 'Гэрчилгээний мэдэгдэл' : 'Certificate Notifications',
      description: language === 'mn'
        ? 'Гэрчилгээ олгох болон амжилтын мэдэгдэл'
        : 'Notifications about certificates and achievements'
    },
    {
      key: 'promotionalEmails' as keyof NotificationPreferences,
      icon: <Gift className="h-5 w-5" />,
      title: language === 'mn' ? 'Урамшууллын и-мэйл' : 'Promotional Emails',
      description: language === 'mn'
        ? 'Хөнгөлөлт, купон болон онцгой саналын мэдэгдэл'
        : 'Notifications about discounts, coupons, and special offers'
    }
  ];

  if (isLoading) {
    return (
      <Card className="border-purple-500/20 bg-black/60 backdrop-blur-md">
        <CardContent className="p-8">
          <div className="animate-pulse space-y-4">
            <div className="h-4 bg-gray-700/50 rounded w-1/4"></div>
            <div className="space-y-3">
              {[1, 2, 3, 4, 5].map((i) => (
                <div key={i} className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <div className="h-5 w-5 bg-gray-700/50 rounded"></div>
                    <div className="space-y-1">
                      <div className="h-4 bg-gray-700/50 rounded w-32"></div>
                      <div className="h-3 bg-gray-700/50 rounded w-48"></div>
                    </div>
                  </div>
                  <div className="h-6 w-11 bg-gray-700/50 rounded-full"></div>
                </div>
              ))}
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
    >
      <Card className="border-purple-500/20 bg-black/60 backdrop-blur-md">
        <CardHeader>
          <div className="flex items-center gap-3">
            <Bell className="h-6 w-6 text-purple-400" />
            <div>
              <CardTitle className="text-white">
                {language === 'mn' ? 'Мэдэгдлийн тохиргоо' : 'Notification Settings'}
              </CardTitle>
              <p className="text-gray-400 text-sm">
                {language === 'mn' 
                  ? 'Та хүлээн авахыг хүсэж буй мэдэгдлийн төрлийг сонгоно уу'
                  : 'Choose which notifications you want to receive'
                }
              </p>
            </div>
          </div>
        </CardHeader>

        <CardContent className="space-y-6">
          {/* Master Toggle */}
          <div className="p-4 bg-purple-500/10 rounded-lg border border-purple-500/20">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <Mail className="h-5 w-5 text-purple-400" />
                <div>
                  <Label className="text-white font-medium">
                    {language === 'mn' ? 'И-мэйл мэдэгдэл' : 'Email Notifications'}
                  </Label>
                  <p className="text-sm text-gray-400">
                    {language === 'mn' 
                      ? 'Бүх и-мэйл мэдэгдлийг идэвхжүүлэх/идэвхгүй болгох'
                      : 'Enable or disable all email notifications'
                    }
                  </p>
                </div>
              </div>
              <Switch
                checked={preferences.emailNotifications}
                onCheckedChange={(value) => handlePreferenceChange('emailNotifications', value)}
              />
            </div>
          </div>

          <Separator className="bg-gray-600/30" />

          {/* Individual Settings */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-white">
              {language === 'mn' ? 'Мэдэгдлийн төрөл' : 'Notification Types'}
            </h3>
            
            {notificationTypes.filter(type => !type.master).map((type) => (
              <motion.div
                key={type.key}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.3 }}
                className={`flex items-center justify-between p-4 rounded-lg border transition-all ${
                  preferences.emailNotifications
                    ? 'border-gray-600/30 bg-gray-700/20'
                    : 'border-gray-600/10 bg-gray-700/10 opacity-50'
                }`}
              >
                <div className="flex items-center gap-3">
                  <div className={`p-2 rounded-lg ${
                    preferences.emailNotifications 
                      ? 'bg-purple-500/20 text-purple-400' 
                      : 'bg-gray-600/20 text-gray-500'
                  }`}>
                    {type.icon}
                  </div>
                  <div>
                    <Label className={`font-medium ${
                      preferences.emailNotifications ? 'text-white' : 'text-gray-500'
                    }`}>
                      {type.title}
                    </Label>
                    <p className={`text-sm ${
                      preferences.emailNotifications ? 'text-gray-400' : 'text-gray-600'
                    }`}>
                      {type.description}
                    </p>
                  </div>
                </div>
                <Switch
                  checked={preferences[type.key] && preferences.emailNotifications}
                  onCheckedChange={(value) => handlePreferenceChange(type.key, value)}
                  disabled={!preferences.emailNotifications}
                />
              </motion.div>
            ))}
          </div>

          {/* Email Frequency Info */}
          <div className="p-4 bg-blue-500/10 rounded-lg border border-blue-500/20">
            <div className="flex items-start gap-3">
              <Bell className="h-5 w-5 text-blue-400 mt-0.5" />
              <div>
                <h4 className="font-medium text-blue-400 mb-1">
                  {language === 'mn' ? 'И-мэйлийн давтамж' : 'Email Frequency'}
                </h4>
                <ul className="text-sm text-blue-300 space-y-1">
                  <li>• {language === 'mn' ? 'Хичээлийн шинэчлэл: Шууд' : 'Course updates: Immediate'}</li>
                  <li>• {language === 'mn' ? 'Явцын сануулга: 7 хоногт 1 удаа' : 'Progress reminders: Weekly'}</li>
                  <li>• {language === 'mn' ? 'Урамшуулал: Сард 2-3 удаа' : 'Promotions: 2-3 times per month'}</li>
                </ul>
              </div>
            </div>
          </div>

          {/* Save Button */}
          <div className="flex items-center justify-between pt-4">
            <div className="flex items-center gap-2">
              {saved && (
                <motion.div
                  initial={{ opacity: 0, scale: 0.8 }}
                  animate={{ opacity: 1, scale: 1 }}
                  className="flex items-center gap-2 text-green-400"
                >
                  <CheckCircle className="h-4 w-4" />
                  <span className="text-sm">
                    {language === 'mn' ? 'Амжилттай хадгалагдлаа' : 'Settings saved successfully'}
                  </span>
                </motion.div>
              )}
            </div>
            
            <Button
              onClick={handleSave}
              disabled={isSaving}
              className="bg-purple-500 hover:bg-purple-600 text-white"
            >
              <Save className="h-4 w-4 mr-2" />
              {isSaving 
                ? (language === 'mn' ? 'Хадгалж байна...' : 'Saving...')
                : (language === 'mn' ? 'Хадгалах' : 'Save Settings')
              }
            </Button>
          </div>

          {/* Unsubscribe Info */}
          <div className="text-center pt-4 border-t border-gray-600/30">
            <p className="text-xs text-gray-500">
              {language === 'mn' 
                ? 'Та ямар ч үед и-мэйлийн доод хэсгээс бүртгэлээ цуцлах боломжтой'
                : 'You can unsubscribe from any email using the link at the bottom of each message'
              }
            </p>
          </div>
        </CardContent>
      </Card>
    </motion.div>
  );
}
