'use client';

import { useState, useRef } from 'react';
import { motion, useMotionValue, useSpring, useTransform } from 'framer-motion';
import Image from 'next/image';
import { cn } from '@/lib/utils';
import { useMobile } from '@/hooks/use-mobile';

interface TeamMemberCardProps {
  readonly name: string;
  readonly role: string;
  readonly image: string;
  readonly className?: string;
  readonly index?: number;
}

export function TeamMemberCard({
  name,
  role,
  image,
  className,
  index = 0,
}: Readonly<TeamMemberCardProps>) {
  const [isHovered, setIsHovered] = useState(false);
  const ref = useRef<HTMLDivElement>(null);
  const { isMobile, isTouchDevice } = useMobile();

  // Mouse position values
  const mouseX = useMotionValue(0);
  const mouseY = useMotionValue(0);

  // Smooth spring physics for mouse movement
  const springConfig = { damping: 25, stiffness: 300 };
  const smoothMouseX = useSpring(mouseX, springConfig);
  const smoothMouseY = useSpring(mouseY, springConfig);

  // Transform mouse position into rotation values
  const rotateX = useTransform(smoothMouseY, [-100, 100], [5, -5]);
  const rotateY = useTransform(smoothMouseX, [-100, 100], [-5, 5]);

  // For the glow effect position
  const [glowPosition, setGlowPosition] = useState({ x: 50, y: 50 });

  // Handle mouse move (disabled on mobile for performance)
  const handleMouseMove = (e: React.MouseEvent<HTMLDivElement>) => {
    if (ref.current && !isMobile) {
      const rect = ref.current.getBoundingClientRect();

      // Calculate center point
      const centerX = rect.left + rect.width / 2;
      const centerY = rect.top + rect.height / 2;

      // Set values for 3D rotation effect
      mouseX.set(e.clientX - centerX);
      mouseY.set(e.clientY - centerY);

      // Calculate percentage position for glow effect
      const x = ((e.clientX - rect.left) / rect.width) * 100;
      const y = ((e.clientY - rect.top) / rect.height) * 100;
      setGlowPosition({ x, y });
    }
  };

  return (
    <motion.div
      ref={ref}
      className={cn(
        'relative overflow-hidden rounded-xl aspect-[3/4]',
        'transform-gpu', // Hardware acceleration
        'shadow-lg shadow-black/30', // Add subtle shadow
        'will-change-transform', // Optimize for animations
        isMobile ? 'cursor-default active:scale-95' : 'cursor-pointer',
        isTouchDevice && 'touch-manipulation',
        className
      )}
      initial={{ opacity: 0, y: 20 }}
      whileInView={{ opacity: 1, y: 0 }}
      transition={{
        duration: 0.7,
        delay: index * 0.1,
        ease: [0.19, 1, 0.22, 1] // Custom easing for smoother animation
      }}
      viewport={{ once: true, margin: "-50px", amount: 0.1 }} // Reduced threshold for better performance
      onMouseEnter={() => !isMobile && setIsHovered(true)}
      onMouseLeave={() => {
        if (!isMobile) {
          setIsHovered(false);
          // Reset mouse position when leaving
          mouseX.set(0);
          mouseY.set(0);
        }
      }}
      onMouseMove={handleMouseMove}
      onTouchStart={() => isTouchDevice && setIsHovered(true)}
      onTouchEnd={() => isTouchDevice && setIsHovered(false)}
      style={{
        rotateX: isMobile ? 0 : rotateX,
        rotateY: isMobile ? 0 : rotateY,
        scale: isHovered ? (isMobile ? 1.02 : 1.03) : 1,
        transformStyle: isMobile ? "flat" : "preserve-3d",
        perspective: isMobile ? "none" : 1000,
        transition: "transform 0.7s cubic-bezier(0.175, 0.885, 0.32, 1.275)" // More specific transition property
      }}
      whileHover={!isMobile ? {
        boxShadow: "0 20px 40px rgba(0, 0, 0, 0.5)",
      } : undefined}
      whileTap={isTouchDevice ? { scale: 0.98 } : undefined}
    >
      {/* Dark overlay that fades out on hover */}
      <motion.div
        className="absolute inset-0 z-10 bg-black/50"
        initial={{ opacity: 1 }}
        animate={{ opacity: isHovered ? 0 : 1 }}
        transition={{ duration: 0.3 }}
      />

      {/* Colorful glow effect that follows cursor */}
      <motion.div
        className="absolute inset-0 z-0 opacity-0"
        initial={{ opacity: 0 }}
        animate={{
          opacity: isHovered ? 0.6 : 0,
        }}
        transition={{ duration: 0.3 }}
        style={{
          background: `radial-gradient(circle at ${glowPosition.x}% ${glowPosition.y}%, hsl(var(--primary)) 0%, transparent 80%)`,
          filter: 'blur(15px)',
        }}
      />

      {/* Image with grayscale filter that transitions to color on hover */}
      <motion.div
        className="relative w-full h-full will-change-transform"
        animate={{
          scale: isHovered ? 1.05 : 1 // Reduced zoom amount for better performance
        }}
        transition={{
          duration: 0.8,
          ease: [0.19, 1, 0.22, 1] // Custom easing for smooth animation
        }}
      >
        <Image
          src={image}
          alt={name}
          fill
          priority={index < 4} // Prioritize loading first 4 images
          loading={index < 4 ? "eager" : "lazy"} // Eager loading for first 4 images
          sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
          className={cn(
            'object-cover will-change-transform',
            !isHovered ? 'grayscale brightness-60 contrast-110 transition-all duration-700' : 'transition-all duration-500'
          )}
        />
      </motion.div>

      {/* Content overlay at the bottom */}
      <motion.div
        className="absolute bottom-0 left-0 right-0 p-4 bg-gradient-to-t from-black/80 to-transparent z-20"
        animate={{
          y: isHovered ? 0 : 5,
          opacity: isHovered ? 1 : 0.8
        }}
        transition={{ duration: 0.3 }}
      >
        <motion.h3
          className="font-medium text-white text-lg"
          animate={{
            scale: isHovered ? 1.05 : 1,
            color: isHovered ? "rgb(255, 255, 255)" : "rgb(220, 220, 220)"
          }}
          transition={{ duration: 0.3 }}
        >
          {name}
        </motion.h3>
        <motion.p
          className="text-sm text-primary"
          animate={{
            y: isHovered ? 0 : 3,
            opacity: isHovered ? 1 : 0.8
          }}
          transition={{ duration: 0.3, delay: 0.05 }}
        >
          {role}
        </motion.p>
      </motion.div>
    </motion.div>
  );
}
