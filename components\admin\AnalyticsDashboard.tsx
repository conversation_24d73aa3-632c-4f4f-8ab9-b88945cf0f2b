'use client';

import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { 
  Users, 
  BookOpen, 
  DollarSign, 
  TrendingUp, 
  TrendingDown,
  Eye,
  Clock,
  Award,
  Download,
  RefreshCw,
  BarChart3,
  PieChart,
  Activity,
  Target
} from 'lucide-react';
import { useLanguage } from '@/lib/context/language-context';
import analyticsService, { DashboardMetrics, CourseAnalytics, UserEngagement, RevenueAnalytics } from '@/lib/services/analyticsService';

export default function AnalyticsDashboard() {
  const { language } = useLanguage();
  const [timeRange, setTimeRange] = useState<'7d' | '30d' | '90d' | '1y'>('30d');
  const [isLoading, setIsLoading] = useState(true);
  const [metrics, setMetrics] = useState<DashboardMetrics | null>(null);
  const [courseAnalytics, setCourseAnalytics] = useState<CourseAnalytics[]>([]);
  const [userEngagement, setUserEngagement] = useState<UserEngagement | null>(null);
  const [revenueAnalytics, setRevenueAnalytics] = useState<RevenueAnalytics | null>(null);

  useEffect(() => {
    loadAnalytics();
  }, [timeRange]);

  const loadAnalytics = async () => {
    setIsLoading(true);
    try {
      // Try to load real analytics from Appwrite
      try {
        const [metricsData, courseData, engagementData, revenueData] = await Promise.all([
          analyticsService.getDashboardMetrics(timeRange),
          analyticsService.getCourseAnalytics(timeRange),
          analyticsService.getUserEngagement(timeRange),
          analyticsService.getRevenueAnalytics(timeRange)
        ]);

        setMetrics(metricsData);
        setCourseAnalytics(courseData);
        setUserEngagement(engagementData);
        setRevenueAnalytics(revenueData);
      } catch (appwriteError) {
        console.log('Appwrite analytics not available, using mock data:', appwriteError);

        // Fallback to default empty data if Appwrite fails
        const defaultMetrics = {
          totalUsers: 0,
          totalCourses: 0,
          totalEnrollments: 0,
          totalRevenue: 0,
          activeUsers: 0,
          completionRate: 0,
          averageRating: 0,
          growthRate: 0
        };

        const defaultCourseAnalytics: any[] = [];
        const defaultUserEngagement = {
          dailyActiveUsers: 0,
          weeklyActiveUsers: 0,
          monthlyActiveUsers: 0,
          averageSessionDuration: 0,
          bounceRate: 0,
          retentionRate: 0
        };

        const defaultRevenueAnalytics = {
          totalRevenue: 0,
          monthlyRecurring: 0,
          averageOrderValue: 0,
          conversionRate: 0,
          refundRate: 0,
          revenueGrowth: 0,
          revenueByPaymentMethod: []
        };

        setMetrics(defaultMetrics);
        setCourseAnalytics(defaultCourseAnalytics);
        setUserEngagement(defaultUserEngagement);
        setRevenueAnalytics(defaultRevenueAnalytics);
      }
    } catch (error) {
      console.error('Failed to load analytics:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const formatNumber = (num: number): string => {
    if (num >= 1000000) {
      return (num / 1000000).toFixed(1) + 'M';
    } else if (num >= 1000) {
      return (num / 1000).toFixed(1) + 'K';
    }
    return num.toString();
  };

  const formatCurrency = (amount: number): string => {
    return new Intl.NumberFormat('mn-MN', {
      style: 'currency',
      currency: 'MNT',
      minimumFractionDigits: 0
    }).format(amount);
  };

  const MetricCard = ({ 
    title, 
    value, 
    change, 
    icon: Icon, 
    format = 'number' 
  }: {
    title: string;
    value: number;
    change?: number;
    icon: any;
    format?: 'number' | 'currency' | 'percentage';
  }) => (
    <Card className="border-purple-500/20 bg-black/60 backdrop-blur-md">
      <CardContent className="p-6">
        <div className="flex items-center justify-between">
          <div>
            <p className="text-gray-400 text-sm font-medium">{title}</p>
            <p className="text-2xl font-bold text-white">
              {format === 'currency' ? formatCurrency(value) :
               format === 'percentage' ? `${value.toFixed(1)}%` :
               formatNumber(value)}
            </p>
            {change !== undefined && (
              <div className={`flex items-center gap-1 text-sm ${
                change >= 0 ? 'text-green-400' : 'text-red-400'
              }`}>
                {change >= 0 ? <TrendingUp className="h-3 w-3" /> : <TrendingDown className="h-3 w-3" />}
                <span>{Math.abs(change).toFixed(1)}%</span>
              </div>
            )}
          </div>
          <div className="p-3 bg-purple-500/20 rounded-lg">
            <Icon className="h-6 w-6 text-purple-400" />
          </div>
        </div>
      </CardContent>
    </Card>
  );

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div className="animate-pulse">
            <div className="h-8 bg-gray-700/50 rounded w-48 mb-2"></div>
            <div className="h-4 bg-gray-700/50 rounded w-32"></div>
          </div>
          <div className="h-10 bg-gray-700/50 rounded w-32"></div>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {[1, 2, 3, 4].map((i) => (
            <div key={i} className="animate-pulse">
              <Card className="border-purple-500/20 bg-black/60">
                <CardContent className="p-6">
                  <div className="h-4 bg-gray-700/50 rounded w-24 mb-2"></div>
                  <div className="h-8 bg-gray-700/50 rounded w-16 mb-2"></div>
                  <div className="h-3 bg-gray-700/50 rounded w-12"></div>
                </CardContent>
              </Card>
            </div>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-white">
            {language === 'mn' ? 'Аналитик самбар' : 'Analytics Dashboard'}
          </h1>
          <p className="text-gray-400">
            {language === 'mn' ? 'Платформын гүйцэтгэлийн тайлан' : 'Platform performance insights'}
          </p>
        </div>
        
        <div className="flex items-center gap-3">
          <Select value={timeRange} onValueChange={(value: any) => setTimeRange(value)}>
            <SelectTrigger className="w-32 bg-black/40 border-purple-500/20 text-white">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="7d">7 Days</SelectItem>
              <SelectItem value="30d">30 Days</SelectItem>
              <SelectItem value="90d">90 Days</SelectItem>
              <SelectItem value="1y">1 Year</SelectItem>
            </SelectContent>
          </Select>
          
          <Button
            onClick={loadAnalytics}
            variant="outline"
            size="sm"
            className="border-purple-500/30 text-purple-400 hover:bg-purple-500/10"
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
        </div>
      </div>

      {/* Key Metrics */}
      {metrics && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <MetricCard
            title={language === 'mn' ? 'Нийт хэрэглэгч' : 'Total Users'}
            value={metrics.totalUsers}
            change={metrics.growthRate}
            icon={Users}
          />
          <MetricCard
            title={language === 'mn' ? 'Нийт хичээл' : 'Total Courses'}
            value={metrics.totalCourses}
            icon={BookOpen}
          />
          <MetricCard
            title={language === 'mn' ? 'Нийт орлого' : 'Total Revenue'}
            value={metrics.totalRevenue}
            icon={DollarSign}
            format="currency"
          />
          <MetricCard
            title={language === 'mn' ? 'Дуусгах хувь' : 'Completion Rate'}
            value={metrics.completionRate}
            icon={Award}
            format="percentage"
          />
        </div>
      )}

      {/* Detailed Analytics */}
      <Tabs defaultValue="overview" className="space-y-6">
        <TabsList className="bg-black/40 border border-purple-500/20">
          <TabsTrigger value="overview" className="data-[state=active]:bg-purple-500/20">
            {language === 'mn' ? 'Ерөнхий' : 'Overview'}
          </TabsTrigger>
          <TabsTrigger value="courses" className="data-[state=active]:bg-purple-500/20">
            {language === 'mn' ? 'Хичээлүүд' : 'Courses'}
          </TabsTrigger>
          <TabsTrigger value="users" className="data-[state=active]:bg-purple-500/20">
            {language === 'mn' ? 'Хэрэглэгчид' : 'Users'}
          </TabsTrigger>
          <TabsTrigger value="revenue" className="data-[state=active]:bg-purple-500/20">
            {language === 'mn' ? 'Орлого' : 'Revenue'}
          </TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Active Users Chart */}
            <Card className="border-purple-500/20 bg-black/60 backdrop-blur-md">
              <CardHeader>
                <CardTitle className="text-white flex items-center gap-2">
                  <Activity className="h-5 w-5 text-purple-400" />
                  {language === 'mn' ? 'Идэвхтэй хэрэглэгчид' : 'Active Users'}
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="h-64 flex items-center justify-center text-gray-400">
                  <BarChart3 className="h-16 w-16" />
                  <span className="ml-4">Chart visualization would go here</span>
                </div>
              </CardContent>
            </Card>

            {/* Top Performing Courses */}
            <Card className="border-purple-500/20 bg-black/60 backdrop-blur-md">
              <CardHeader>
                <CardTitle className="text-white flex items-center gap-2">
                  <Target className="h-5 w-5 text-purple-400" />
                  {language === 'mn' ? 'Шилдэг хичээлүүд' : 'Top Courses'}
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {courseAnalytics?.slice(0, 5).map((course, index) => (
                    <div key={course.courseId} className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        <Badge variant="outline" className="border-purple-500/30 text-purple-400">
                          #{index + 1}
                        </Badge>
                        <div>
                          <p className="text-white font-medium">{course.title}</p>
                          <p className="text-gray-400 text-sm">{course.enrollments} enrollments</p>
                        </div>
                      </div>
                      <div className="text-right">
                        <p className="text-white">{course.completionRate.toFixed(1)}%</p>
                        <p className="text-gray-400 text-sm">completion</p>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="courses" className="space-y-6">
          <Card className="border-purple-500/20 bg-black/60 backdrop-blur-md">
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle className="text-white">
                  {language === 'mn' ? 'Хичээлийн гүйцэтгэл' : 'Course Performance'}
                </CardTitle>
                <Button
                  variant="outline"
                  size="sm"
                  className="border-purple-500/30 text-purple-400 hover:bg-purple-500/10"
                >
                  <Download className="h-4 w-4 mr-2" />
                  Export
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {courseAnalytics?.map((course) => (
                  <motion.div
                    key={course.courseId}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    className="p-4 bg-gray-800/30 rounded-lg border border-gray-700/30"
                  >
                    <div className="flex items-center justify-between mb-3">
                      <h3 className="text-white font-semibold">{course.title}</h3>
                      <Badge className="bg-purple-500/20 text-purple-400">
                        {formatCurrency(course.revenue)}
                      </Badge>
                    </div>
                    
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-3">
                      <div>
                        <p className="text-gray-400 text-sm">Enrollments</p>
                        <p className="text-white font-medium">{course.enrollments}</p>
                      </div>
                      <div>
                        <p className="text-gray-400 text-sm">Completions</p>
                        <p className="text-white font-medium">{course.completions}</p>
                      </div>
                      <div>
                        <p className="text-gray-400 text-sm">Avg Progress</p>
                        <p className="text-white font-medium">{course.averageProgress.toFixed(1)}%</p>
                      </div>
                      <div>
                        <p className="text-gray-400 text-sm">Rating</p>
                        <p className="text-white font-medium">⭐ {course.rating}</p>
                      </div>
                    </div>
                    
                    <div className="space-y-2">
                      <div className="flex justify-between text-sm">
                        <span className="text-gray-400">Completion Rate</span>
                        <span className="text-white">{course.completionRate.toFixed(1)}%</span>
                      </div>
                      <Progress value={course.completionRate} className="h-2" />
                    </div>
                  </motion.div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="users" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card className="border-purple-500/20 bg-black/60 backdrop-blur-md">
              <CardHeader>
                <CardTitle className="text-white">
                  {language === 'mn' ? 'Хэрэглэгчийн идэвхжил' : 'User Activity'}
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="h-64 flex items-center justify-center text-gray-400">
                  <Activity className="h-16 w-16" />
                  <span className="ml-4">User activity chart</span>
                </div>
              </CardContent>
            </Card>

            <Card className="border-purple-500/20 bg-black/60 backdrop-blur-md">
              <CardHeader>
                <CardTitle className="text-white">
                  {language === 'mn' ? 'Төхөөрөмжийн хуваарилалт' : 'Device Breakdown'}
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <span className="text-gray-400">Mobile</span>
                    <span className="text-white">65%</span>
                  </div>
                  <Progress value={65} className="h-2" />
                  
                  <div className="flex items-center justify-between">
                    <span className="text-gray-400">Desktop</span>
                    <span className="text-white">30%</span>
                  </div>
                  <Progress value={30} className="h-2" />
                  
                  <div className="flex items-center justify-between">
                    <span className="text-gray-400">Tablet</span>
                    <span className="text-white">5%</span>
                  </div>
                  <Progress value={5} className="h-2" />
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="revenue" className="space-y-6">
          {revenueAnalytics && (
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card className="border-purple-500/20 bg-black/60 backdrop-blur-md">
                <CardHeader>
                  <CardTitle className="text-white">
                    {language === 'mn' ? 'Сарын орлого' : 'Monthly Revenue'}
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="h-64 flex items-center justify-center text-gray-400">
                    <BarChart3 className="h-16 w-16" />
                    <span className="ml-4">Revenue chart</span>
                  </div>
                </CardContent>
              </Card>

              <Card className="border-purple-500/20 bg-black/60 backdrop-blur-md">
                <CardHeader>
                  <CardTitle className="text-white">
                    {language === 'mn' ? 'Төлбөрийн аргаар' : 'Payment Methods'}
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {revenueAnalytics?.revenueByPaymentMethod?.map((method) => (
                      <div key={method.method} className="flex items-center justify-between">
                        <span className="text-gray-400">{method.method}</span>
                        <div className="flex items-center gap-2">
                          <span className="text-white">{method.percentage}%</span>
                          <span className="text-gray-400 text-sm">
                            {formatCurrency(method.revenue)}
                          </span>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
}
