/* Mobile-specific optimizations and utilities */

/* CSS Custom Properties for Mobile */
:root {
  --vh: 1vh;
  --viewport-height: 100vh;
  --safe-area-inset-top: env(safe-area-inset-top);
  --safe-area-inset-bottom: env(safe-area-inset-bottom);
  --safe-area-inset-left: env(safe-area-inset-left);
  --safe-area-inset-right: env(safe-area-inset-right);
}

/* Mobile viewport height fix */
.min-h-screen-mobile {
  min-height: 100vh;
  min-height: calc(var(--vh, 1vh) * 100);
}

.h-screen-mobile {
  height: 100vh;
  height: calc(var(--vh, 1vh) * 100);
}

/* Safe area utilities */
.pt-safe-top {
  padding-top: var(--safe-area-inset-top);
}

.pb-safe-bottom {
  padding-bottom: var(--safe-area-inset-bottom);
}

.pl-safe-left {
  padding-left: var(--safe-area-inset-left);
}

.pr-safe-right {
  padding-right: var(--safe-area-inset-right);
}

.p-safe {
  padding-top: var(--safe-area-inset-top);
  padding-bottom: var(--safe-area-inset-bottom);
  padding-left: var(--safe-area-inset-left);
  padding-right: var(--safe-area-inset-right);
}

/* Touch target optimization */
.touch-target {
  min-height: 44px;
  min-width: 44px;
}

.touch-target-large {
  min-height: 56px;
  min-width: 56px;
}

/* Mobile-specific animations */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

/* Mobile performance optimizations */
@media (max-width: 768px) {
  /* Reduce animation complexity on mobile */
  .mobile-reduce-motion {
    animation: none !important;
    transition: none !important;
  }
  
  /* Optimize transforms for mobile */
  .mobile-optimized-transform {
    transform: translateZ(0);
    will-change: transform;
    backface-visibility: hidden;
  }
  
  /* Reduce blur effects on low-end devices */
  .mobile-reduce-blur {
    backdrop-filter: none !important;
    filter: none !important;
  }
  
  /* Optimize shadows for mobile */
  .mobile-reduce-shadow {
    box-shadow: none !important;
    text-shadow: none !important;
  }
}

/* Mobile-specific layout utilities */
@media (max-width: 640px) {
  .mobile-stack {
    flex-direction: column !important;
  }
  
  .mobile-full-width {
    width: 100% !important;
  }
  
  .mobile-center {
    text-align: center !important;
  }
  
  .mobile-hide {
    display: none !important;
  }
  
  .mobile-show {
    display: block !important;
  }
}

/* Tablet-specific utilities */
@media (min-width: 641px) and (max-width: 1024px) {
  .tablet-hide {
    display: none !important;
  }
  
  .tablet-show {
    display: block !important;
  }
}

/* Desktop-specific utilities */
@media (min-width: 1025px) {
  .desktop-hide {
    display: none !important;
  }
  
  .desktop-show {
    display: block !important;
  }
}

/* Mobile typography optimizations */
@media (max-width: 768px) {
  .mobile-text-sm {
    font-size: 0.875rem !important;
    line-height: 1.25rem !important;
  }
  
  .mobile-text-base {
    font-size: 1rem !important;
    line-height: 1.5rem !important;
  }
  
  .mobile-text-lg {
    font-size: 1.125rem !important;
    line-height: 1.75rem !important;
  }
  
  .mobile-text-xl {
    font-size: 1.25rem !important;
    line-height: 1.75rem !important;
  }
  
  .mobile-text-2xl {
    font-size: 1.5rem !important;
    line-height: 2rem !important;
  }
  
  .mobile-text-3xl {
    font-size: 1.875rem !important;
    line-height: 2.25rem !important;
  }
}

/* Mobile spacing utilities */
@media (max-width: 768px) {
  .mobile-p-2 {
    padding: 0.5rem !important;
  }
  
  .mobile-p-4 {
    padding: 1rem !important;
  }
  
  .mobile-p-6 {
    padding: 1.5rem !important;
  }
  
  .mobile-m-2 {
    margin: 0.5rem !important;
  }
  
  .mobile-m-4 {
    margin: 1rem !important;
  }
  
  .mobile-m-6 {
    margin: 1.5rem !important;
  }
  
  .mobile-px-4 {
    padding-left: 1rem !important;
    padding-right: 1rem !important;
  }
  
  .mobile-py-8 {
    padding-top: 2rem !important;
    padding-bottom: 2rem !important;
  }
  
  .mobile-py-12 {
    padding-top: 3rem !important;
    padding-bottom: 3rem !important;
  }
}

/* Mobile grid optimizations */
@media (max-width: 768px) {
  .mobile-grid-1 {
    grid-template-columns: 1fr !important;
  }
  
  .mobile-grid-2 {
    grid-template-columns: repeat(2, 1fr) !important;
  }
  
  .mobile-gap-4 {
    gap: 1rem !important;
  }
  
  .mobile-gap-6 {
    gap: 1.5rem !important;
  }
}

/* Mobile scroll optimizations */
.mobile-scroll-smooth {
  scroll-behavior: smooth;
  -webkit-overflow-scrolling: touch;
}

.mobile-scroll-snap {
  scroll-snap-type: x mandatory;
}

.mobile-scroll-snap-item {
  scroll-snap-align: start;
}

/* Mobile focus states */
@media (max-width: 768px) {
  .mobile-focus:focus {
    outline: 2px solid #8b5cf6;
    outline-offset: 2px;
  }
  
  .mobile-focus-visible:focus-visible {
    outline: 2px solid #8b5cf6;
    outline-offset: 2px;
  }
}

/* Mobile hover states (touch devices) */
@media (hover: none) and (pointer: coarse) {
  .hover\:scale-105:hover {
    transform: none;
  }
  
  .hover\:bg-purple-700:hover {
    background-color: inherit;
  }
  
  /* Add active states for touch */
  .touch-active:active {
    transform: scale(0.95);
    background-color: rgba(139, 92, 246, 0.1);
  }
}

/* Mobile keyboard adjustments */
.keyboard-open {
  padding-bottom: 0 !important;
}

.keyboard-open .fixed-bottom {
  position: relative !important;
}

/* Mobile orientation utilities */
@media (orientation: landscape) and (max-height: 500px) {
  .landscape-mobile-hide {
    display: none !important;
  }
  
  .landscape-mobile-compact {
    padding-top: 0.5rem !important;
    padding-bottom: 0.5rem !important;
  }
}

/* Mobile performance classes */
.mobile-gpu-accelerated {
  transform: translateZ(0);
  will-change: transform;
  backface-visibility: hidden;
}

.mobile-contain-layout {
  contain: layout;
}

.mobile-contain-paint {
  contain: paint;
}

.mobile-contain-strict {
  contain: strict;
}

/* Mobile accessibility improvements */
@media (max-width: 768px) {
  .mobile-sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
  }
  
  .mobile-not-sr-only {
    position: static;
    width: auto;
    height: auto;
    padding: 0;
    margin: 0;
    overflow: visible;
    clip: auto;
    white-space: normal;
  }
}

/* Mobile dark mode optimizations */
@media (max-width: 768px) and (prefers-color-scheme: dark) {
  .mobile-dark-optimized {
    background-color: #000000;
    color: #ffffff;
  }
  
  .mobile-dark-text {
    color: rgba(255, 255, 255, 0.9);
  }
  
  .mobile-dark-border {
    border-color: rgba(255, 255, 255, 0.1);
  }
}
