import { NextRequest, NextResponse } from 'next/server';
import { createSupabaseServerClient } from '@/lib/supabase/client';

export async function POST(request: NextRequest) {
  try {
    const serverClient = createSupabaseServerClient();
    
    // Check admin access
    const { data: { session }, error: sessionError } = await serverClient.auth.getSession();
    if (sessionError || !session) {
      return NextResponse.json({ error: 'Not authenticated' }, { status: 401 });
    }

    const { data: user } = await serverClient
      .from('users')
      .select('role')
      .eq('id', session.user.id)
      .single();

    if (!user || user.role !== 'ADMIN') {
      return NextResponse.json({ error: 'Access denied' }, { status: 403 });
    }

    // Create sample categories
    const categories = [
      { name: 'Programming', slug: 'programming', color: '#8B5CF6', description: 'Learn programming languages and frameworks' },
      { name: 'Business', slug: 'business', color: '#10B981', description: 'Business and entrepreneurship courses' },
      { name: 'Design', slug: 'design', color: '#F59E0B', description: 'UI/UX and graphic design courses' },
      { name: 'Marketing', slug: 'marketing', color: '#EF4444', description: 'Digital marketing and growth strategies' }
    ];

    const { data: createdCategories } = await serverClient
      .from('course_categories')
      .upsert(categories, { onConflict: 'slug' })
      .select();

    // Create sample courses
    const sampleCourses = [
      {
        title: 'React.js Fundamentals',
        description: 'Learn the basics of React.js and build modern web applications',
        price: 99000,
        instructor: 'John Doe',
        category_id: createdCategories?.[0]?.id,
        duration_minutes: 480,
        difficulty_level: 'beginner',
        is_published: true,
        is_featured: true,
        enrollment_count: 25,
        rating: 4.8,
        tags: ['React', 'JavaScript', 'Frontend'],
        prerequisites: ['Basic HTML', 'Basic CSS', 'JavaScript fundamentals'],
        learning_outcomes: ['Build React components', 'Manage state with hooks', 'Create interactive UIs']
      },
      {
        title: 'Digital Marketing Mastery',
        description: 'Complete guide to digital marketing strategies and tools',
        price: 149000,
        instructor: 'Jane Smith',
        category_id: createdCategories?.[3]?.id,
        duration_minutes: 720,
        difficulty_level: 'intermediate',
        is_published: true,
        is_featured: false,
        enrollment_count: 18,
        rating: 4.6,
        tags: ['Marketing', 'SEO', 'Social Media'],
        prerequisites: ['Basic marketing knowledge'],
        learning_outcomes: ['Create marketing campaigns', 'Analyze marketing metrics', 'Use marketing tools']
      },
      {
        title: 'UI/UX Design Principles',
        description: 'Learn the fundamentals of user interface and user experience design',
        price: 129000,
        instructor: 'Mike Johnson',
        category_id: createdCategories?.[2]?.id,
        duration_minutes: 600,
        difficulty_level: 'beginner',
        is_published: true,
        is_featured: true,
        enrollment_count: 32,
        rating: 4.9,
        tags: ['Design', 'UI', 'UX', 'Figma'],
        prerequisites: ['Basic design knowledge'],
        learning_outcomes: ['Create user-centered designs', 'Use design tools', 'Conduct user research']
      },
      {
        title: 'Startup Business Planning',
        description: 'Everything you need to know to start and grow your business',
        price: 199000,
        instructor: 'Sarah Wilson',
        category_id: createdCategories?.[1]?.id,
        duration_minutes: 900,
        difficulty_level: 'intermediate',
        is_published: false,
        is_featured: false,
        enrollment_count: 0,
        rating: 0,
        tags: ['Business', 'Startup', 'Planning'],
        prerequisites: ['Basic business knowledge'],
        learning_outcomes: ['Create business plans', 'Understand funding', 'Build business models']
      }
    ];

    const { data: createdCourses } = await serverClient
      .from('courses')
      .insert(sampleCourses)
      .select();

    // Create sample blog posts
    const sampleBlogs = [
      {
        title: 'The Future of Web Development in 2024',
        content: `
# The Future of Web Development in 2024

Web development continues to evolve at a rapid pace. Here are the key trends shaping the industry:

## 1. AI-Powered Development Tools
Artificial intelligence is revolutionizing how we write code. Tools like GitHub Copilot and ChatGPT are becoming essential for developers.

## 2. Server-Side Rendering Renaissance
With frameworks like Next.js and Nuxt.js, server-side rendering is making a comeback for better performance and SEO.

## 3. WebAssembly Growth
WebAssembly is enabling high-performance applications in the browser, opening new possibilities for web development.

## Conclusion
The future of web development is exciting, with new technologies making development faster and more efficient.
        `,
        excerpt: 'Explore the latest trends and technologies shaping web development in 2024',
        author_id: session.user.id,
        is_published: true,
        is_featured: true,
        view_count: 156,
        tags: ['Web Development', 'Technology', 'Trends'],
        meta_title: 'Future of Web Development 2024 - InnoHub',
        meta_description: 'Discover the latest web development trends and technologies for 2024',
        published_at: new Date().toISOString()
      },
      {
        title: 'Building Your First Startup: A Complete Guide',
        content: `
# Building Your First Startup: A Complete Guide

Starting a business can be overwhelming, but with the right approach, you can increase your chances of success.

## 1. Validate Your Idea
Before investing time and money, make sure there's a market for your product or service.

## 2. Create a Business Plan
A solid business plan will guide your decisions and help you secure funding.

## 3. Build Your MVP
Start with a minimum viable product to test your assumptions and gather feedback.

## 4. Focus on Customer Acquisition
Without customers, there's no business. Focus on finding and retaining customers.

## Conclusion
Building a startup is challenging but rewarding. Take it one step at a time.
        `,
        excerpt: 'Learn the essential steps to build and launch your first successful startup',
        author_id: session.user.id,
        is_published: true,
        is_featured: false,
        view_count: 89,
        tags: ['Startup', 'Business', 'Entrepreneurship'],
        meta_title: 'How to Build Your First Startup - InnoHub',
        meta_description: 'Complete guide to building and launching your first startup successfully',
        published_at: new Date(Date.now() - 86400000).toISOString() // Yesterday
      },
      {
        title: 'Design Thinking for Developers',
        content: `
# Design Thinking for Developers

As a developer, understanding design thinking can make you more effective at building user-centered applications.

## What is Design Thinking?
Design thinking is a human-centered approach to innovation that integrates the needs of people, the possibilities of technology, and requirements for business success.

## The 5 Stages
1. **Empathize** - Understand your users
2. **Define** - Frame the problem
3. **Ideate** - Generate solutions
4. **Prototype** - Build to think
5. **Test** - Learn from users

## Why Developers Should Care
Understanding your users leads to better products and more successful projects.
        `,
        excerpt: 'How developers can use design thinking to build better user experiences',
        author_id: session.user.id,
        is_published: false,
        is_featured: false,
        view_count: 0,
        tags: ['Design', 'Development', 'UX'],
        meta_title: 'Design Thinking for Developers - InnoHub',
        meta_description: 'Learn how to apply design thinking principles in software development'
      }
    ];

    const { data: createdBlogs } = await serverClient
      .from('blog_posts')
      .insert(sampleBlogs)
      .select();

    return NextResponse.json({
      success: true,
      message: 'Sample data created successfully',
      data: {
        categories: createdCategories?.length || 0,
        courses: createdCourses?.length || 0,
        blogs: createdBlogs?.length || 0
      }
    });

  } catch (error: any) {
    console.error('Seed data error:', error);
    return NextResponse.json(
      { error: 'Failed to create sample data' },
      { status: 500 }
    );
  }
}
