# InnoHub - Innovation Hub for Startups

InnoHub is a comprehensive web application built with Next.js that serves as an innovation hub for startups, providing resources, mentorship, and educational content.

## Features

- **Multi-language Support**: Full Mongolian and English language support
- **Course System**: Comprehensive course platform with authentication and progress tracking
- **Admin Dashboard**: Content management for courses, blogs, and analytics
- **Payment Integration**: Mongolian payment methods integration
- **Responsive Design**: Mobile-optimized with modern UI components
- **Authentication**: Secure user authentication and authorization

## Getting Started

First, run the development server:

```bash
npm run dev
```

Open [http://localhost:3001](http://localhost:3001) with your browser to see the result.

## Tech Stack

- **Framework**: Next.js 15.3.2 with App Router
- **Styling**: Tailwind CSS with custom animations
- **UI Components**: Shadcn/ui, Aceternity UI, Framer Motion
- **Authentication**: Custom auth system with Supabase
- **Database**: Supabase (PostgreSQL)
- **Language**: TypeScript

## Project Structure

- `/app` - Next.js app router pages and layouts
- `/components` - Reusable UI components
- `/lib` - Utility functions, contexts, and configurations
- `/public` - Static assets and images
- `/contexts` - React context providers

## Development

The application uses modern development practices:

- TypeScript for type safety
- ESLint for code quality
- Tailwind CSS for styling
- Framer Motion for animations

## Contributing

This is a private project for InnoHub. For any questions or contributions, please contact the development team.
