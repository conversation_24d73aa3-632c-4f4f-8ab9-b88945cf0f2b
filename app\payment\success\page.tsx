'use client';

import { useEffect, useState } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { motion } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { CheckCircle, BookOpen, ArrowRight, Home } from 'lucide-react';
import Link from 'next/link';

export default function PaymentSuccessPage() {
  const [isLoading, setIsLoading] = useState(true);
  const [paymentData, setPaymentData] = useState<any>(null);
  const router = useRouter();
  const searchParams = useSearchParams();
  const orderId = searchParams.get('orderId');

  useEffect(() => {
    // Simulate payment verification
    setTimeout(() => {
      setPaymentData({
        orderId,
        courseName: 'InnoHub Accelerator Program',
        amount: 150000,
        transactionId: 'TXN_' + Date.now()
      });
      setIsLoading(false);
    }, 2000);
  }, [orderId]);

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('mn-MN', {
      style: 'currency',
      currency: 'MNT',
      minimumFractionDigits: 0
    }).format(price);
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-purple-900 via-black to-purple-900 flex items-center justify-center p-4">
        <Card className="border-purple-500/20 bg-black/60 backdrop-blur-md w-full max-w-md">
          <CardContent className="p-8 text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-purple-500 mx-auto mb-4"></div>
            <p className="text-white">Төлбөр баталгаажуулж байна...</p>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-900 via-black to-purple-900 flex items-center justify-center p-4">
      <motion.div
        initial={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ duration: 0.5 }}
        className="w-full max-w-md"
      >
        <Card className="border-green-500/20 bg-black/60 backdrop-blur-md">
          <CardHeader className="text-center space-y-4">
            <motion.div
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              transition={{ delay: 0.2, type: "spring", stiffness: 200 }}
              className="mx-auto w-16 h-16 bg-green-500/20 rounded-full flex items-center justify-center"
            >
              <CheckCircle className="h-8 w-8 text-green-400" />
            </motion.div>
            <div>
              <CardTitle className="text-2xl font-bold text-white">
                Төлбөр амжилттай!
              </CardTitle>
              <p className="text-gray-400 mt-2">
                Таны төлбөр амжилттай хийгдлээ
              </p>
            </div>
          </CardHeader>

          <CardContent className="space-y-6">
            {/* Payment Details */}
            <div className="bg-green-500/10 border border-green-500/20 rounded-lg p-4 space-y-3">
              <div className="flex justify-between">
                <span className="text-gray-400">Хичээл:</span>
                <span className="text-white font-medium">{paymentData?.courseName}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-400">Дүн:</span>
                <span className="text-green-400 font-bold">
                  {formatPrice(paymentData?.amount || 0)}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-400">Гүйлгээний дугаар:</span>
                <span className="text-white text-sm font-mono">
                  {paymentData?.transactionId}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-400">Захиалгын дугаар:</span>
                <span className="text-white text-sm font-mono">
                  {paymentData?.orderId}
                </span>
              </div>
            </div>

            {/* Success Message */}
            <div className="text-center space-y-2">
              <h3 className="text-white font-medium">
                🎉 Баяр хүргэе!
              </h3>
              <p className="text-gray-400 text-sm">
                Та одоо хичээлүүдэд хандах боломжтой боллоо. 
                Амжилт хүсье!
              </p>
            </div>

            {/* Action Buttons */}
            <div className="space-y-3">
              <Button
                onClick={() => router.push('/courses')}
                className="w-full bg-purple-600 hover:bg-purple-700 text-white"
              >
                <BookOpen className="h-4 w-4 mr-2" />
                Хичээлүүд рүү очих
                <ArrowRight className="h-4 w-4 ml-2" />
              </Button>
              
              <Link href="/" className="block">
                <Button
                  variant="outline"
                  className="w-full border-gray-600 text-gray-300 hover:bg-gray-800"
                >
                  <Home className="h-4 w-4 mr-2" />
                  Нүүр хуудас руу буцах
                </Button>
              </Link>
            </div>

            {/* Email Notice */}
            <div className="bg-blue-500/10 border border-blue-500/20 rounded-lg p-3">
              <p className="text-blue-400 text-sm text-center">
                📧 Баталгаажуулах имэйл таны хаяг руу илгээгдлээ
              </p>
            </div>
          </CardContent>
        </Card>
      </motion.div>
    </div>
  );
}
