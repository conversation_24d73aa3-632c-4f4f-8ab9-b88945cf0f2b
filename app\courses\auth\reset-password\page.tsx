'use client';

import { useState, useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { motion } from 'framer-motion';
import { Lock, CheckCircle } from 'lucide-react';

import AuthLayout from '@/components/auth/AuthLayout';
import AuthInput from '@/components/auth/AuthInput';
import AuthButton from '@/components/auth/AuthButton';
import AuthAlert from '@/components/auth/AuthAlert';
import PasswordStrength from '@/components/auth/PasswordStrength';
import { createSupabaseClient } from '@/lib/supabase/client';

export default function ResetPasswordPage() {
  const [formData, setFormData] = useState({
    password: '',
    confirmPassword: ''
  });
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [showPasswordStrength, setShowPasswordStrength] = useState(false);
  const [isValidSession, setIsValidSession] = useState(false);
  
  const router = useRouter();
  const searchParams = useSearchParams();
  const supabase = createSupabaseClient();

  useEffect(() => {
    // Check if we have a valid password reset session
    const checkSession = async () => {
      const { data: { session }, error } = await supabase.auth.getSession();
      
      if (error || !session) {
        setError('Invalid or expired password reset link. Please request a new one.');
        return;
      }
      
      setIsValidSession(true);
    };

    checkSession();
  }, [supabase.auth]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
    
    // Clear error when user starts typing
    if (error) setError('');
    
    // Show password strength when user starts typing password
    if (name === 'password') {
      setShowPasswordStrength(value.length > 0);
    }
  };

  const validateForm = () => {
    if (!formData.password) {
      setError('Please enter a new password');
      return false;
    }

    if (formData.password.length < 8) {
      setError('Password must be at least 8 characters long');
      return false;
    }

    if (formData.password !== formData.confirmPassword) {
      setError('Passwords do not match');
      return false;
    }

    return true;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError('');

    if (!validateForm()) {
      setIsLoading(false);
      return;
    }

    try {
      const { error } = await supabase.auth.updateUser({
        password: formData.password
      });

      if (error) {
        setError(error.message);
      } else {
        setSuccess('Password updated successfully! Redirecting to login...');
        
        // Sign out to clear the reset session
        await supabase.auth.signOut();
        
        // Redirect to login after a brief delay
        setTimeout(() => {
          router.push('/courses/auth/login?message=password-reset-success');
        }, 2000);
      }
    } catch (error: any) {
      console.error('Password reset error:', error);
      setError('An unexpected error occurred. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  if (!isValidSession && !error) {
    return (
      <AuthLayout
        title="Reset Password"
        subtitle="Verifying your password reset link..."
        backHref="/courses/auth/login"
        backText="Back to Login"
      >
        <div className="text-center py-8">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-purple-500 mx-auto"></div>
          <p className="text-gray-400 mt-4">Verifying your session...</p>
        </div>
      </AuthLayout>
    );
  }

  if (error && !isValidSession) {
    return (
      <AuthLayout
        title="Reset Password"
        subtitle="There was an issue with your password reset link"
        backHref="/courses/auth/login"
        backText="Back to Login"
      >
        <div className="space-y-6">
          <AuthAlert
            type="error"
            message={error}
            show={true}
            dismissible={false}
          />
          
          <div className="text-center">
            <AuthButton
              onClick={() => router.push('/courses/auth/forgot-password')}
              variant="outline"
              className="w-full"
            >
              Request New Reset Link
            </AuthButton>
          </div>
        </div>
      </AuthLayout>
    );
  }

  return (
    <AuthLayout
      title="Set New Password"
      subtitle="Enter your new password below"
      backHref="/courses/auth/login"
      backText="Back to Login"
    >
      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Alert Messages */}
        <AuthAlert
          type="error"
          message={error}
          show={!!error}
          onClose={() => setError('')}
        />
        
        <AuthAlert
          type="success"
          message={success}
          show={!!success}
          dismissible={false}
        />

        {/* Password Input */}
        <div>
          <AuthInput
            label="New Password"
            name="password"
            type="password"
            icon={Lock}
            placeholder="Enter your new password"
            value={formData.password}
            onChange={handleInputChange}
            showPasswordToggle
            required
            autoComplete="new-password"
          />
          
          <PasswordStrength
            password={formData.password}
            show={showPasswordStrength}
          />
        </div>

        {/* Confirm Password Input */}
        <AuthInput
          label="Confirm New Password"
          name="confirmPassword"
          type="password"
          icon={Lock}
          placeholder="Confirm your new password"
          value={formData.confirmPassword}
          onChange={handleInputChange}
          showPasswordToggle
          required
          autoComplete="new-password"
          error={formData.confirmPassword && formData.password !== formData.confirmPassword ? 'Passwords do not match' : undefined}
        />

        {/* Submit Button */}
        <AuthButton
          type="submit"
          loading={isLoading}
          icon={CheckCircle}
          className="w-full"
          disabled={!formData.password || !formData.confirmPassword}
        >
          {isLoading ? 'Updating Password...' : 'Update Password'}
        </AuthButton>
      </form>
    </AuthLayout>
  );
}
