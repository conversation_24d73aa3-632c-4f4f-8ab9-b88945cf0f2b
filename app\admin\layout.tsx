'use client';

import { useEffect, useState } from 'react';
import { useRouter, usePathname } from 'next/navigation';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import {
  LayoutDashboard,
  BookOpen,
  FileText,
  Users,
  Settings,
  BarChart3,
  LogOut,
  Menu,
  X
} from 'lucide-react';
// import { useAuth } from '@/contexts/AuthContext';

interface AdminLayoutProps {
  children: React.ReactNode;
}

export default function AdminLayout({ children }: AdminLayoutProps) {
  const [isAdmin, setIsAdmin] = useState(false);
  const [loading, setLoading] = useState(true);
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const router = useRouter();
  const pathname = usePathname();
  // const { signOut } = useAuth();

  useEffect(() => {
    // Skip admin check for setup and login pages
    if (pathname === '/admin/setup' || pathname === '/admin/login') {
      setLoading(false);
      setIsAdmin(false);
      return;
    }

    checkAdminAccess();
  }, [pathname]);

  const checkAdminAccess = async () => {
    try {
      const response = await fetch('/api/auth/check-admin');

      if (response.ok) {
        const result = await response.json();
        if (result.isAdmin) {
          setIsAdmin(true);
          setLoading(false);
          return;
        }
      }

      // Not admin or error, redirect to login
      router.push('/admin/login');
    } catch (error) {
      console.error('Admin check error:', error);
      router.push('/admin/login');
    }
  };

  const handleSignOut = async () => {
    try {
      // Simple sign out by clearing session
      const response = await fetch('/api/auth/logout', { method: 'POST' });
      if (response.ok) {
        router.push('/admin/login');
      }
    } catch (error) {
      console.error('Sign out error:', error);
      router.push('/admin/login');
    }
  };

  const navigation = [
    {
      name: 'Dashboard',
      href: '/admin/dashboard',
      icon: LayoutDashboard,
      current: pathname === '/admin/dashboard'
    },
    {
      name: 'Courses',
      href: '/admin/courses',
      icon: BookOpen,
      current: pathname.startsWith('/admin/courses')
    },
    {
      name: 'Blog Posts',
      href: '/admin/blogs',
      icon: FileText,
      current: pathname.startsWith('/admin/blogs')
    },
    {
      name: 'Users',
      href: '/admin/users',
      icon: Users,
      current: pathname.startsWith('/admin/users')
    },
    {
      name: 'Analytics',
      href: '/admin/analytics',
      icon: BarChart3,
      current: pathname.startsWith('/admin/analytics')
    },
    {
      name: 'Settings',
      href: '/admin/settings',
      icon: Settings,
      current: pathname.startsWith('/admin/settings')
    }
  ];

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-purple-900 via-black to-purple-900 flex items-center justify-center">
        <div className="text-white">Loading...</div>
      </div>
    );
  }

  if (!isAdmin) {
    return null;
  }

  // Don't show layout for login and setup pages
  if (pathname === '/admin/login' || pathname === '/admin/setup') {
    return <>{children}</>;
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-900 via-black to-purple-900">
      {/* Mobile sidebar overlay */}
      {sidebarOpen && (
        <div
          className="fixed inset-0 z-40 lg:hidden"
          onClick={() => setSidebarOpen(false)}
        >
          <div className="fixed inset-0 bg-black/50" />
        </div>
      )}

      {/* Sidebar */}
      <div className={`
        fixed inset-y-0 left-0 z-50 w-64 bg-black/80 backdrop-blur-xl border-r border-purple-500/20 transform transition-transform duration-300 ease-in-out lg:translate-x-0
        ${sidebarOpen ? 'translate-x-0' : '-translate-x-full'}
      `}>
        <div className="flex h-full flex-col">
          {/* Logo */}
          <div className="flex h-16 items-center justify-between px-6 border-b border-purple-500/20">
            <div className="flex items-center">
              <div className="w-8 h-8 bg-purple-600 rounded-lg flex items-center justify-center mr-3">
                <span className="text-white font-bold text-sm">IH</span>
              </div>
              <span className="text-white font-semibold">InnoHub Admin</span>
            </div>
            <Button
              variant="ghost"
              size="sm"
              className="lg:hidden text-white"
              onClick={() => setSidebarOpen(false)}
            >
              <X className="w-4 h-4" />
            </Button>
          </div>

          {/* Navigation */}
          <nav className="flex-1 px-4 py-6 space-y-2">
            {navigation.map((item) => {
              const Icon = item.icon;
              return (
                <Link
                  key={item.name}
                  href={item.href}
                  className={`
                    flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-colors
                    ${item.current
                      ? 'bg-purple-600 text-white'
                      : 'text-gray-300 hover:bg-purple-600/20 hover:text-white'
                    }
                  `}
                  onClick={() => setSidebarOpen(false)}
                >
                  <Icon className="w-5 h-5 mr-3" />
                  {item.name}
                </Link>
              );
            })}
          </nav>

          {/* Sign out */}
          <div className="p-4 border-t border-purple-500/20">
            <Button
              onClick={handleSignOut}
              variant="ghost"
              className="w-full justify-start text-gray-300 hover:bg-red-600/20 hover:text-red-400"
            >
              <LogOut className="w-5 h-5 mr-3" />
              Sign Out
            </Button>
          </div>
        </div>
      </div>

      {/* Main content */}
      <div className="lg:pl-64">
        {/* Mobile header */}
        <div className="sticky top-0 z-30 flex h-16 items-center gap-x-4 border-b border-purple-500/20 bg-black/80 backdrop-blur-xl px-4 lg:hidden">
          <Button
            variant="ghost"
            size="sm"
            className="text-white"
            onClick={() => setSidebarOpen(true)}
          >
            <Menu className="w-5 h-5" />
          </Button>
          <div className="flex items-center">
            <div className="w-6 h-6 bg-purple-600 rounded flex items-center justify-center mr-2">
              <span className="text-white font-bold text-xs">IH</span>
            </div>
            <span className="text-white font-semibold">InnoHub Admin</span>
          </div>
        </div>

        {/* Page content */}
        <main className="min-h-screen">
          {children}
        </main>
      </div>
    </div>
  );
}
