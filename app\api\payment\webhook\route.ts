import { NextRequest, NextResponse } from 'next/server';
import { appwriteUserService } from '@/lib/appwrite/users';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { provider, transactionId, status, amount, orderId } = body;

    console.log('Payment webhook received:', {
      provider,
      transactionId,
      status,
      amount,
      orderId
    });

    // Verify webhook authenticity (implement based on provider)
    if (!verifyWebhook(request, body)) {
      return NextResponse.json(
        { error: 'Invalid webhook signature' },
        { status: 401 }
      );
    }

    // Process successful payment
    if (status === 'completed' || status === 'paid') {
      await processSuccessfulPayment({
        orderId,
        transactionId,
        amount,
        provider
      });
    }

    return NextResponse.json({ success: true });
  } catch (error: any) {
    console.error('Webhook processing error:', error);
    return NextResponse.json(
      { error: 'Webhook processing failed' },
      { status: 500 }
    );
  }
}

function verifyWebhook(request: NextRequest, body: any): boolean {
  // Implement webhook signature verification based on provider
  // For now, return true (implement proper verification in production)
  return true;
}

async function processSuccessfulPayment(paymentData: {
  orderId: string;
  transactionId: string;
  amount: number;
  provider: string;
}) {
  try {
    // Extract course and user info from orderId
    // Format: INNOHUB_${courseId}_${userId}_${timestamp}
    const orderParts = paymentData.orderId.split('_');
    if (orderParts.length >= 4) {
      const courseId = orderParts[1];
      const userId = orderParts[2];

      // Create enrollment record
      // You can implement this with Appwrite when you create the enrollments collection
      console.log('Creating enrollment:', {
        courseId,
        userId,
        paymentData
      });

      // Send confirmation email
      // You can implement email service here
      console.log('Sending confirmation email for successful payment');
    }
  } catch (error) {
    console.error('Error processing successful payment:', error);
  }
}
