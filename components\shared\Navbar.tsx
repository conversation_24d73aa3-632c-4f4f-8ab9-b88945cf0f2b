'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { usePathname } from 'next/navigation';
import { motion } from 'framer-motion';
import { cn } from '@/lib/utils';
import { Sheet, SheetContent, SheetTrigger } from '@/components/ui/sheet';
import { Menu, Globe, GraduationCap } from 'lucide-react';
import { useLanguage } from '@/lib/context/language-context';
import { HoverBorderGradient } from '@/components/ui/hover-border-gradient';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu';

const navLinks = [
  { href: '/', key: 'nav.home' },
  { href: '/about', key: 'nav.about' },
  { href: '/programs', key: 'nav.programs' },
  { href: '/success-stories', key: 'nav.successStories' },
  { href: '/investors', key: 'nav.investors' },
  { href: '/news', key: 'nav.news' },
];

export default function Navbar() {
  const [scrolled, setScrolled] = useState(false);
  const pathname = usePathname();
  const { t, language, setLanguage, isLoaded } = useLanguage();

  useEffect(() => {
    const handleScroll = () => {
      const isScrolled = window.scrollY > 10;
      if (isScrolled !== scrolled) {
        setScrolled(isScrolled);
      }
    };

    window.addEventListener('scroll', handleScroll);
    return () => {
      window.removeEventListener('scroll', handleScroll);
    };
  }, [scrolled]);

  return (
    <header
      className={`fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${
        scrolled ? 'bg-black/80 backdrop-blur-md border-b border-purple-500' : 'bg-transparent'
      }`}
    >
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between h-16">
          {/* Logo (Left) */}
          <div className="flex items-center">
            <Link href="/" className="flex items-center gap-2">
              <div className="relative w-10 h-10 rounded-full overflow-hidden">
                <Image
                  src="/images/logo/innohub_logo.png"
                  alt="InnoHub Logo"
                  width={40}
                  height={40}
                  className="object-cover"
                />
              </div>
              <span className="font-bold text-xl text-white">INNO<span className="text-purple-500">HUB</span></span>
            </Link>
          </div>

          {/* Desktop Navigation (Center) */}
          <div className="hidden md:flex items-center space-x-4">
            {navLinks.map((link) => (
              <Link
                key={link.href}
                href={link.href}
                className={`relative text-white/80 hover:text-white transition-colors text-sm font-medium px-3 py-2 rounded-md ${
                  pathname === link.href ? 'text-white' : ''
                }`}
              >
                {pathname === link.href && (
                  <span className="absolute inset-x-0 bottom-0 h-0.5 bg-purple-600" />
                )}
                {isLoaded ? t(link.key) : ''}
              </Link>
            ))}
          </div>

          {/* Right Side Actions */}
          <div className="flex items-center gap-4">
            {/* Courses Button */}
            <Link href="/courses" className="hidden md:block">
              <HoverBorderGradient
                containerClassName="rounded-full"
                className="dark:bg-slate-950 bg-slate-950 text-white px-4 py-1.5 text-sm"
                duration={2}
              >
                {isLoaded ? t('nav.courses') : 'Courses'}
              </HoverBorderGradient>
            </Link>

            {/* Language Switcher */}
            {isLoaded && (
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <HoverBorderGradient
                    containerClassName="rounded-full h-[2.25rem] w-[3rem]"
                    className="dark:bg-slate-950 bg-slate-950 text-white flex items-center justify-center gap-1 h-full w-full px-2"
                    duration={2}
                  >
                    <Globe className="h-4 w-4" />
                    <span className="text-sm">{language === 'en' ? '🇺🇸' : '🇲🇳'}</span>
                    <span className="sr-only">{t('nav.language')}</span>
                  </HoverBorderGradient>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end" className="bg-black/90 backdrop-blur-md border border-purple-500 rounded-xl">
                  <DropdownMenuItem
                    className={`${language === 'en' ? 'bg-purple-700/30' : ''} text-white hover:bg-black/70 rounded-lg my-1 flex items-center gap-2`}
                    onClick={() => setLanguage('en')}
                  >
                    <span className="text-lg">🇺🇸</span>
                    {t('nav.english')}
                  </DropdownMenuItem>
                  <DropdownMenuItem
                    className={`${language === 'mn' ? 'bg-purple-700/30' : ''} text-white hover:bg-black/70 rounded-lg my-1 flex items-center gap-2`}
                    onClick={() => setLanguage('mn')}
                  >
                    <span className="text-lg">🇲🇳</span>
                    {t('nav.mongolian')}
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            )}

            {/* Mobile menu button - Enhanced for Touch */}
            <div className="md:hidden">
              <Sheet>
                <SheetTrigger asChild>
                  <motion.button
                    className="relative flex items-center justify-center w-14 h-14 rounded-full bg-black/50 backdrop-blur-md border border-purple-500/40 text-white shadow-lg shadow-purple-500/20 active:bg-black/70"
                    whileTap={{ scale: 0.92 }}
                    whileHover={{ scale: 1.05 }}
                    transition={{ duration: 0.2 }}
                    style={{ minHeight: '56px', minWidth: '56px' }} // Ensure minimum touch target size
                  >
                    <Menu className="h-7 w-7" />
                    <span className="sr-only">Toggle menu</span>
                  </motion.button>
                </SheetTrigger>
                <SheetContent
                  side="left"
                  className="w-full sm:w-[400px] bg-black/95 backdrop-blur-xl border-purple-500/50 p-0 overflow-y-auto"
                >
                  <div className="flex flex-col h-full min-h-screen">
                    {/* Header */}
                    <div className="flex items-center justify-between p-6 border-b border-purple-500/20 sticky top-0 bg-black/95 backdrop-blur-xl z-10">
                      <div className="flex items-center gap-3">
                        <div className="relative w-10 h-10 rounded-full overflow-hidden">
                          <Image
                            src="/images/logo/innohub_logo.png"
                            alt="InnoHub Logo"
                            width={40}
                            height={40}
                            className="object-cover"
                          />
                        </div>
                        <span className="font-bold text-xl text-white">INNO<span className="text-purple-500">HUB</span></span>
                      </div>
                    </div>

                    {/* Navigation Links */}
                    <nav className="flex-1 px-6 py-4">
                      <div className="space-y-2">
                        {navLinks.map((link, index) => {
                          const isActive = pathname === link.href

                          return (
                            <motion.div
                              key={link.href}
                              initial={{ opacity: 0, x: -20 }}
                              animate={{ opacity: 1, x: 0 }}
                              transition={{ duration: 0.3, delay: index * 0.1 }}
                            >
                              <Link
                                href={link.href}
                                className={cn(
                                  "flex items-center gap-4 p-5 rounded-xl transition-all duration-300 min-h-[64px]",
                                  "text-white/80 hover:text-white hover:bg-purple-500/15",
                                  "active:scale-95 active:bg-purple-500/25 touch-manipulation",
                                  "focus:outline-none focus:ring-2 focus:ring-purple-500/50",
                                  isActive && "bg-purple-500/20 text-white border border-purple-500/30"
                                )}
                              >
                                <span className="text-lg font-medium">
                                  {isLoaded ? t(link.key) : ''}
                                </span>
                                {isActive && (
                                  <motion.div
                                    className="ml-auto w-2 h-2 bg-purple-500 rounded-full"
                                    initial={{ scale: 0 }}
                                    animate={{ scale: 1 }}
                                    transition={{ duration: 0.3 }}
                                  />
                                )}
                              </Link>
                            </motion.div>
                          )
                        })}

                        {/* Courses Link */}
                        <motion.div
                          initial={{ opacity: 0, x: -20 }}
                          animate={{ opacity: 1, x: 0 }}
                          transition={{ duration: 0.3, delay: navLinks.length * 0.1 }}
                        >
                          <Link
                            href="/courses"
                            className="flex items-center gap-4 p-5 rounded-xl transition-all duration-300 min-h-[64px] text-white/80 hover:text-white hover:bg-purple-500/15 active:scale-95 active:bg-purple-500/25 touch-manipulation focus:outline-none focus:ring-2 focus:ring-purple-500/50"
                          >
                            <GraduationCap className="h-5 w-5 flex-shrink-0" />
                            <span className="text-lg font-medium">
                              {isLoaded ? t('nav.courses') : 'Courses'}
                            </span>
                          </Link>
                        </motion.div>
                      </div>
                    </nav>

                    {/* Language Switcher */}
                    {isLoaded && (
                      <div className="p-6 border-t border-purple-500/20 mt-auto">
                        <p className="text-white/60 text-base mb-4">{t('nav.language')}:</p>
                        <div className="flex gap-3">
                          <motion.button
                            onClick={() => setLanguage('en')}
                            className={cn(
                              "flex items-center gap-3 px-5 py-4 rounded-xl transition-all duration-300 min-h-[56px] flex-1 touch-manipulation",
                              "focus:outline-none focus:ring-2 focus:ring-purple-500/50",
                              language === 'en'
                                ? 'bg-purple-500/30 text-white border border-purple-500/50'
                                : 'bg-white/5 text-white/80 hover:bg-white/10 active:bg-white/15'
                            )}
                            whileTap={{ scale: 0.95 }}
                          >
                            <span className="text-lg">🇺🇸</span>
                            <span className="text-base font-medium">{t('nav.english')}</span>
                          </motion.button>
                          <motion.button
                            onClick={() => setLanguage('mn')}
                            className={cn(
                              "flex items-center gap-3 px-5 py-4 rounded-xl transition-all duration-300 min-h-[56px] flex-1 touch-manipulation",
                              "focus:outline-none focus:ring-2 focus:ring-purple-500/50",
                              language === 'mn'
                                ? 'bg-purple-500/30 text-white border border-purple-500/50'
                                : 'bg-white/5 text-white/80 hover:bg-white/10 active:bg-white/15'
                            )}
                            whileTap={{ scale: 0.95 }}
                          >
                            <span className="text-lg">🇲🇳</span>
                            <span className="text-base font-medium">{t('nav.mongolian')}</span>
                          </motion.button>
                        </div>
                      </div>
                    )}
                  </div>
                </SheetContent>
              </Sheet>
            </div>
          </div>
        </div>
      </div>
    </header>
  );
}
