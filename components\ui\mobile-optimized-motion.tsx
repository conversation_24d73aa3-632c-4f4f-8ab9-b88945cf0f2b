'use client';

import { motion, MotionProps, Variants } from 'framer-motion';
import { ReactNode } from 'react';
import { useMobilePerformance } from '@/hooks/use-mobile';

interface MobileOptimizedMotionProps extends MotionProps {
  children: ReactNode;
  fallback?: ReactNode;
  enableOnMobile?: boolean;
  mobileVariants?: Variants;
  desktopVariants?: Variants;
}

// Mobile-optimized motion wrapper that conditionally applies animations
export function MobileOptimizedMotion({
  children,
  fallback,
  enableOnMobile = true,
  mobileVariants,
  desktopVariants,
  variants,
  initial,
  animate,
  exit,
  transition,
  ...props
}: MobileOptimizedMotionProps) {
  const { shouldReduceMotion, animationFrameRate } = useMobilePerformance();

  // If motion should be reduced and not enabled on mobile, return fallback or static content
  if (shouldReduceMotion && !enableOnMobile) {
    return fallback ? <>{fallback}</> : <div {...props}>{children}</div>;
  }

  // Use mobile-specific variants if provided
  const optimizedVariants = shouldReduceMotion && mobileVariants 
    ? mobileVariants 
    : desktopVariants || variants;

  // Optimize transition for mobile
  const optimizedTransition = shouldReduceMotion 
    ? {
        duration: 0.2,
        ease: 'easeOut',
        ...transition
      }
    : {
        duration: 0.5,
        ease: [0.22, 1, 0.36, 1],
        ...transition
      };

  return (
    <motion.div
      variants={optimizedVariants}
      initial={shouldReduceMotion ? false : initial}
      animate={animate}
      exit={exit}
      transition={optimizedTransition}
      {...props}
    >
      {children}
    </motion.div>
  );
}

// Predefined mobile-optimized animation variants
export const mobileAnimationVariants = {
  // Fade animations
  fadeIn: {
    mobile: {
      hidden: { opacity: 0 },
      visible: { opacity: 1 }
    },
    desktop: {
      hidden: { opacity: 0, y: 20 },
      visible: { opacity: 1, y: 0 }
    }
  },

  // Slide animations
  slideUp: {
    mobile: {
      hidden: { opacity: 0 },
      visible: { opacity: 1 }
    },
    desktop: {
      hidden: { opacity: 0, y: 40 },
      visible: { opacity: 1, y: 0 }
    }
  },

  // Scale animations
  scaleIn: {
    mobile: {
      hidden: { opacity: 0 },
      visible: { opacity: 1 }
    },
    desktop: {
      hidden: { opacity: 0, scale: 0.8 },
      visible: { opacity: 1, scale: 1 }
    }
  },

  // Stagger animations
  stagger: {
    mobile: {
      hidden: { opacity: 0 },
      visible: {
        opacity: 1,
        transition: {
          staggerChildren: 0.1
        }
      }
    },
    desktop: {
      hidden: { opacity: 0 },
      visible: {
        opacity: 1,
        transition: {
          staggerChildren: 0.2,
          delayChildren: 0.1
        }
      }
    }
  }
};

// Mobile-optimized scroll-triggered animation
interface MobileScrollAnimationProps {
  children: ReactNode;
  className?: string;
  animation?: keyof typeof mobileAnimationVariants;
  threshold?: number;
  once?: boolean;
}

export function MobileScrollAnimation({
  children,
  className,
  animation = 'fadeIn',
  threshold = 0.1,
  once = true
}: MobileScrollAnimationProps) {
  const { shouldReduceMotion } = useMobilePerformance();

  const variants = mobileAnimationVariants[animation];
  const mobileVariants = variants.mobile;
  const desktopVariants = variants.desktop;

  return (
    <MobileOptimizedMotion
      className={className}
      mobileVariants={mobileVariants}
      desktopVariants={desktopVariants}
      initial="hidden"
      whileInView="visible"
      viewport={{ 
        once, 
        amount: shouldReduceMotion ? 0.1 : threshold,
        margin: shouldReduceMotion ? '-50px' : '-100px'
      }}
    >
      {children}
    </MobileOptimizedMotion>
  );
}

// Mobile-optimized hover animation
interface MobileHoverProps {
  children: ReactNode;
  className?: string;
  hoverScale?: number;
  tapScale?: number;
  disabled?: boolean;
}

export function MobileHover({
  children,
  className,
  hoverScale = 1.05,
  tapScale = 0.95,
  disabled = false
}: MobileHoverProps) {
  const { shouldReduceMotion } = useMobilePerformance();

  if (shouldReduceMotion || disabled) {
    return <div className={className}>{children}</div>;
  }

  return (
    <motion.div
      className={className}
      whileHover={{ scale: hoverScale }}
      whileTap={{ scale: tapScale }}
      transition={{ duration: 0.2 }}
    >
      {children}
    </motion.div>
  );
}

// Mobile-optimized parallax component
interface MobileParallaxProps {
  children: ReactNode;
  className?: string;
  offset?: number;
  disabled?: boolean;
}

export function MobileParallax({
  children,
  className,
  offset = 50,
  disabled = false
}: MobileParallaxProps) {
  const { shouldReduceMotion } = useMobilePerformance();

  if (shouldReduceMotion || disabled) {
    return <div className={className}>{children}</div>;
  }

  return (
    <motion.div
      className={className}
      style={{
        y: shouldReduceMotion ? 0 : offset
      }}
    >
      {children}
    </motion.div>
  );
}

// Mobile-optimized loading animation
interface MobileLoadingProps {
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}

const loadingSizes = {
  sm: 'w-4 h-4',
  md: 'w-6 h-6',
  lg: 'w-8 h-8'
};

export function MobileLoading({
  size = 'md',
  className
}: MobileLoadingProps) {
  const { shouldReduceMotion } = useMobilePerformance();

  if (shouldReduceMotion) {
    return (
      <div className={`${loadingSizes[size]} bg-purple-500 rounded-full opacity-50 ${className}`} />
    );
  }

  return (
    <motion.div
      className={`${loadingSizes[size]} bg-purple-500 rounded-full ${className}`}
      animate={{
        scale: [1, 1.2, 1],
        opacity: [0.5, 1, 0.5]
      }}
      transition={{
        duration: 1.5,
        repeat: Infinity,
        ease: 'easeInOut'
      }}
    />
  );
}

// Mobile-optimized intersection observer hook for performance
export function useMobileIntersection(threshold = 0.1) {
  const { shouldReduceMotion } = useMobilePerformance();
  
  return {
    threshold: shouldReduceMotion ? 0.1 : threshold,
    margin: shouldReduceMotion ? '-50px' : '-100px',
    once: true
  };
}
