'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
import { motion } from 'framer-motion';
import Link from 'next/link';
import { Mail, Lock, User, Shield, AlertTriangle, Info } from 'lucide-react';

import AuthLayout from '@/components/auth/AuthLayout';
import AuthInput from '@/components/auth/AuthInput';
import AuthButton from '@/components/auth/AuthButton';
import AuthAlert from '@/components/auth/AuthAlert';
import PasswordStrength from '@/components/auth/PasswordStrength';

export default function AdminRegisterPage() {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    password: '',
    confirmPassword: '',
    adminCode: '',
    agreeToTerms: false
  });
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [showPasswordStrength, setShowPasswordStrength] = useState(false);
  
  const router = useRouter();
  const { user } = useAuth();

  // Redirect if already logged in as admin
  useEffect(() => {
    if (user && user.role === 'ADMIN') {
      router.push('/admin');
    }
  }, [user, router]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value, type, checked } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));
    
    // Clear error when user starts typing
    if (error) setError('');
    
    // Show password strength when user starts typing password
    if (name === 'password') {
      setShowPasswordStrength(value.length > 0);
    }
  };

  const validateForm = () => {
    if (!formData.name.trim()) {
      setError('Please enter your full name');
      return false;
    }

    if (!formData.email) {
      setError('Please enter your email address');
      return false;
    }

    if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      setError('Please enter a valid email address');
      return false;
    }

    if (formData.password.length < 12) {
      setError('Admin password must be at least 12 characters long');
      return false;
    }

    if (formData.password !== formData.confirmPassword) {
      setError('Passwords do not match');
      return false;
    }

    if (!formData.adminCode) {
      setError('Admin authorization code is required');
      return false;
    }

    if (!formData.agreeToTerms) {
      setError('Please agree to the Terms of Service and Privacy Policy');
      return false;
    }

    return true;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError('');

    if (!validateForm()) {
      setIsLoading(false);
      return;
    }

    try {
      // Call the admin registration API
      const response = await fetch('/api/auth/admin-register', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: formData.name,
          email: formData.email,
          password: formData.password,
          adminCode: formData.adminCode
        }),
      });

      const result = await response.json();

      if (result.success) {
        setSuccess('Admin account created successfully! Please wait for approval.');
        
        // Clear form
        setFormData({
          name: '',
          email: '',
          password: '',
          confirmPassword: '',
          adminCode: '',
          agreeToTerms: false
        });
      } else {
        setError(result.error || 'Failed to create admin account');
      }
    } catch (error: any) {
      console.error('Admin registration error:', error);
      setError('An unexpected error occurred. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <AuthLayout
      title="Request Admin Access"
      subtitle="Create an administrator account for InnoHub"
      backHref="/auth/admin/login"
      backText="Back to Admin Login"
    >
      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Security Notice */}
        <motion.div
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-blue-900/20 border border-blue-500/30 rounded-lg p-4 mb-6"
        >
          <div className="flex items-start">
            <Info className="h-5 w-5 text-blue-400 mt-0.5 mr-3 flex-shrink-0" />
            <div>
              <h4 className="text-blue-300 font-medium mb-1">Admin Account Request</h4>
              <p className="text-blue-200/80 text-sm">
                Admin accounts require approval and a valid authorization code. 
                Contact your system administrator for the authorization code.
              </p>
            </div>
          </div>
        </motion.div>

        {/* Alert Messages */}
        <AuthAlert
          type="error"
          message={error}
          show={!!error}
          onClose={() => setError('')}
        />
        
        <AuthAlert
          type="success"
          message={success}
          show={!!success}
          dismissible={false}
        />

        {/* Name Input */}
        <AuthInput
          label="Full Name"
          name="name"
          type="text"
          icon={User}
          placeholder="Enter your full name"
          value={formData.name}
          onChange={handleInputChange}
          required
          autoComplete="name"
        />

        {/* Email Input */}
        <AuthInput
          label="Email Address"
          name="email"
          type="email"
          icon={Mail}
          placeholder="Enter your email"
          value={formData.email}
          onChange={handleInputChange}
          required
          autoComplete="email"
        />

        {/* Admin Code Input */}
        <AuthInput
          label="Admin Authorization Code"
          name="adminCode"
          type="text"
          icon={Shield}
          placeholder="Enter admin authorization code"
          value={formData.adminCode}
          onChange={handleInputChange}
          required
        />

        {/* Password Input */}
        <div>
          <AuthInput
            label="Password (Min. 12 characters)"
            name="password"
            type="password"
            icon={Lock}
            placeholder="Create a strong password"
            value={formData.password}
            onChange={handleInputChange}
            showPasswordToggle
            required
            autoComplete="new-password"
          />
          
          <PasswordStrength
            password={formData.password}
            show={showPasswordStrength}
          />
        </div>

        {/* Confirm Password Input */}
        <AuthInput
          label="Confirm Password"
          name="confirmPassword"
          type="password"
          icon={Lock}
          placeholder="Confirm your password"
          value={formData.confirmPassword}
          onChange={handleInputChange}
          showPasswordToggle
          required
          autoComplete="new-password"
          error={formData.confirmPassword && formData.password !== formData.confirmPassword ? 'Passwords do not match' : undefined}
        />

        {/* Terms Agreement */}
        <div className="flex items-start">
          <input
            type="checkbox"
            name="agreeToTerms"
            checked={formData.agreeToTerms}
            onChange={handleInputChange}
            className="w-4 h-4 mt-1 text-purple-600 bg-gray-900 border-gray-600 rounded focus:ring-purple-500 focus:ring-2"
            required
          />
          <label className="ml-3 text-sm text-gray-400">
            I agree to the{' '}
            <Link href="/terms" className="text-purple-400 hover:text-purple-300">
              Terms of Service
            </Link>
            {' '}and{' '}
            <Link href="/privacy" className="text-purple-400 hover:text-purple-300">
              Privacy Policy
            </Link>
            {' '}and understand the responsibilities of admin access.
          </label>
        </div>

        {/* Submit Button */}
        <AuthButton
          type="submit"
          loading={isLoading}
          icon={Shield}
          className="w-full"
          disabled={!formData.name || !formData.email || !formData.password || !formData.confirmPassword || !formData.adminCode || !formData.agreeToTerms}
        >
          {isLoading ? 'Submitting Request...' : 'Request Admin Access'}
        </AuthButton>

        {/* Sign In Link */}
        <div className="text-center">
          <p className="text-gray-400">
            Already have admin access?{' '}
            <Link
              href="/auth/admin/login"
              className="text-purple-400 hover:text-purple-300 font-medium transition-colors"
            >
              Sign in
            </Link>
          </p>
        </div>
      </form>
    </AuthLayout>
  );
}
