// Mongolian Payment Solutions Integration
import crypto from 'crypto';

export interface PaymentRequest {
  amount: number;
  currency: string;
  description: string;
  orderId: string;
  customerEmail: string;
  customerName: string;
  returnUrl: string;
  cancelUrl: string;
}

export interface PaymentResponse {
  success: boolean;
  paymentUrl?: string;
  transactionId?: string;
  qrCode?: string;
  error?: string;
  provider: string;
}

export interface PaymentStatus {
  status: 'pending' | 'completed' | 'failed' | 'cancelled';
  transactionId: string;
  amount: number;
  provider: string;
}

// QPay Integration (Most popular in Mongolia)
export class QPayService {
  private username: string;
  private password: string;
  private invoiceCode: string;
  private serviceUrl: string;

  constructor() {
    this.username = process.env.QPAY_USERNAME || '';
    this.password = process.env.QPAY_PASSWORD || '';
    this.invoiceCode = process.env.QPAY_INVOICE_CODE || '';
    this.serviceUrl = process.env.QPAY_SERVICE_URL || 'https://merchant.qpay.mn/v2';
  }

  async createPayment(request: PaymentRequest): Promise<PaymentResponse> {
    try {
      // Get QPay access token
      const token = await this.getAccessToken();
      
      const paymentData = {
        invoice_code: this.invoiceCode,
        sender_invoice_no: request.orderId,
        invoice_receiver_code: request.customerEmail,
        invoice_description: request.description,
        amount: request.amount,
        callback_url: request.returnUrl
      };

      const response = await fetch(`${this.serviceUrl}/invoice`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify(paymentData)
      });

      const result = await response.json();

      if (result.invoice_id) {
        return {
          success: true,
          paymentUrl: result.qr_text,
          qrCode: result.qr_image,
          transactionId: result.invoice_id,
          provider: 'QPay'
        };
      }

      return {
        success: false,
        error: result.message || 'QPay payment creation failed',
        provider: 'QPay'
      };
    } catch (error: any) {
      return {
        success: false,
        error: error.message,
        provider: 'QPay'
      };
    }
  }

  private async getAccessToken(): Promise<string> {
    const response = await fetch(`${this.serviceUrl}/auth/token`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        username: this.username,
        password: this.password
      })
    });

    const result = await response.json();
    return result.access_token;
  }

  async checkPaymentStatus(transactionId: string): Promise<PaymentStatus> {
    try {
      const token = await this.getAccessToken();
      
      const response = await fetch(`${this.serviceUrl}/payment/check`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({
          object_type: 'INVOICE',
          object_id: transactionId
        })
      });

      const result = await response.json();
      
      let status: 'pending' | 'completed' | 'failed' | 'cancelled' = 'pending';
      if (result.paid_amount > 0) {
        status = 'completed';
      } else if (result.invoice_status === 'CANCELLED') {
        status = 'cancelled';
      }

      return {
        status,
        transactionId,
        amount: result.paid_amount || 0,
        provider: 'QPay'
      };
    } catch (error) {
      return {
        status: 'failed',
        transactionId,
        amount: 0,
        provider: 'QPay'
      };
    }
  }
}

// Simplified QPay-only Payment Service
export class QPayPaymentService {
  private qpay: QPayService;

  constructor() {
    this.qpay = new QPayService();
  }

  async createPayment(request: PaymentRequest): Promise<PaymentResponse> {
    return this.qpay.createPayment(request);
  }

  async checkPaymentStatus(transactionId: string): Promise<PaymentStatus> {
    return this.qpay.checkPaymentStatus(transactionId);
  }

  getProviderInfo() {
    return {
      id: 'qpay',
      name: 'QPay',
      description: 'Монгол улсын хамгийн түгээмэл цахим төлбөрийн систем',
      features: [
        'Аюулгүй төлбөр',
        'Хурдан гүйлгээ',
        'QR код дэмжлэг',
        '24/7 үйлчилгээ'
      ]
    };
  }
}

export const qpayPayment = new QPayPaymentService();
