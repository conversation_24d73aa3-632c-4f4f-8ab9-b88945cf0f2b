import { NextRequest, NextResponse } from 'next/server';
import { qpayPayment } from '@/lib/payment/mongolian-payment';
import { databaseService } from '@/lib/supabase/database';
import { appwriteUserService } from '@/lib/appwrite/users';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const {
      courseId,
      amount,
      currency = 'MNT',
      userId
    } = body;

    // Validate required fields
    if (!courseId || !amount || !userId) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }

    // Get user details
    const user = await appwriteUserService.getCurrentUser();
    if (!user || user.id !== userId) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Create unique order ID
    const orderId = `INNOHUB_${courseId}_${userId}_${Date.now()}`;

    // Prepare payment request
    const paymentRequest = {
      amount: parseInt(amount),
      currency,
      description: `InnoHub Course Payment - Course ID: ${courseId}`,
      orderId,
      customerEmail: user.email,
      customerName: user.name,
      returnUrl: `${process.env.NEXT_PUBLIC_PAYMENT_SUCCESS_URL}?orderId=${orderId}`,
      cancelUrl: `${process.env.NEXT_PUBLIC_PAYMENT_CANCEL_URL}?orderId=${orderId}`
    };

    // Create payment with QPay
    const paymentResult = await qpayPayment.createPayment(paymentRequest);

    if (paymentResult.success) {
      // Save payment to Supabase database
      const { data: payment, error: dbError } = await databaseService.createPayment({
        user_id: userId,
        course_id: courseId,
        amount,
        currency,
        provider: 'qpay',
        transaction_id: paymentResult.transactionId!,
        status: 'pending',
        payment_data: paymentResult
      });

      if (dbError) {
        console.error('Failed to save payment to database:', dbError);
      }

      return NextResponse.json({
        success: true,
        paymentUrl: paymentResult.paymentUrl,
        qrCode: paymentResult.qrCode,
        transactionId: paymentResult.transactionId,
        orderId,
        provider: paymentResult.provider
      });
    } else {
      return NextResponse.json(
        { error: paymentResult.error },
        { status: 400 }
      );
    }
  } catch (error: any) {
    console.error('Payment creation error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const provider = searchParams.get('provider');
    const transactionId = searchParams.get('transactionId');

    if (!provider || !transactionId) {
      return NextResponse.json(
        { error: 'Missing provider or transactionId' },
        { status: 400 }
      );
    }

    const status = await qpayPayment.checkPaymentStatus(transactionId);
    
    return NextResponse.json(status);
  } catch (error: any) {
    console.error('Payment status check error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
