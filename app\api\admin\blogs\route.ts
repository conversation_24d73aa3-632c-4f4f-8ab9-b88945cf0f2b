import { NextRequest, NextResponse } from 'next/server';
import { createSupabaseServerClient } from '@/lib/supabase/client';

export async function GET(request: NextRequest) {
  try {
    const serverClient = createSupabaseServerClient();
    
    // Check admin access
    const { data: { session }, error: sessionError } = await serverClient.auth.getSession();
    if (sessionError || !session) {
      return NextResponse.json({ error: 'Not authenticated' }, { status: 401 });
    }

    const { data: user } = await serverClient
      .from('users')
      .select('role')
      .eq('id', session.user.id)
      .single();

    if (!user || user.role !== 'ADMIN') {
      return NextResponse.json({ error: 'Access denied' }, { status: 403 });
    }

    // Get blog posts with author information
    const { data: blogs, error } = await serverClient
      .from('blog_posts')
      .select(`
        *,
        author:users!author_id(name, email)
      `)
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Error fetching blogs:', error);
      return NextResponse.json({ error: 'Failed to fetch blog posts' }, { status: 500 });
    }

    return NextResponse.json({ blogs: blogs || [] });

  } catch (error: any) {
    console.error('Blogs API error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const serverClient = createSupabaseServerClient();
    
    // Check admin access
    const { data: { session }, error: sessionError } = await serverClient.auth.getSession();
    if (sessionError || !session) {
      return NextResponse.json({ error: 'Not authenticated' }, { status: 401 });
    }

    const { data: user } = await serverClient
      .from('users')
      .select('role')
      .eq('id', session.user.id)
      .single();

    if (!user || user.role !== 'ADMIN') {
      return NextResponse.json({ error: 'Access denied' }, { status: 403 });
    }

    const blogData = await request.json();

    // Create the blog post
    const { data: blog, error } = await serverClient
      .from('blog_posts')
      .insert({
        title: blogData.title,
        content: blogData.content,
        excerpt: blogData.excerpt,
        author_id: session.user.id,
        featured_image_url: blogData.featured_image_url,
        is_published: blogData.is_published || false,
        is_featured: blogData.is_featured || false,
        tags: blogData.tags || [],
        meta_title: blogData.meta_title,
        meta_description: blogData.meta_description,
        published_at: blogData.is_published ? new Date().toISOString() : null
      })
      .select()
      .single();

    if (error) {
      console.error('Error creating blog:', error);
      return NextResponse.json({ error: 'Failed to create blog post' }, { status: 500 });
    }

    return NextResponse.json({ blog });

  } catch (error: any) {
    console.error('Create blog error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
