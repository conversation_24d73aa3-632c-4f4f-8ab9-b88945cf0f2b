"use client";

import React, { useRef, useState, useCallback } from "react";
import { TextHoverEffect } from "./text-hover-effect";

export default function TextHoverEffectFooterAware() {
  const containerRef = useRef<HTMLDivElement>(null);
  const [isFooterHovered, setIsFooterHovered] = useState(false);

  // Check if mouse is over footer elements
  const checkFooterHover = useCallback((e: MouseEvent) => {
    const target = e.target as HTMLElement;
    const isOverFooter = target.closest('footer') !== null;
    setIsFooterHovered(isOverFooter);
  }, []);

  // Handle mouse move to track footer hover state
  const handleMouseMove = useCallback((e: React.MouseEvent) => {
    const target = e.target as HTMLElement;
    const isOverFooter = target.closest('footer') !== null;
    setIsFooterHovered(isOverFooter);
  }, []);

  return (
    <div 
      ref={containerRef}
      className="h-[34rem] flex items-center justify-center"
      onMouseMove={handleMouseMove}
      style={{
        // Disable pointer events when footer is hovered
        pointerEvents: isFooterHovered ? 'none' : 'auto',
        transition: 'opacity 0.2s ease-in-out'
      }}
    >
      <TextHoverEffect text="INNOHUB" />
    </div>
  );
}
