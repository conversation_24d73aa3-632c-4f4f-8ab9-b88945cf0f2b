<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Z-Index Test</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            height: 100vh;
            background: black;
            color: white;
            font-family: Arial, sans-serif;
        }
        
        .text-hover-effect {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 600px;
            z-index: -10;
            display: flex;
            align-items: center;
            justify-content: center;
            pointer-events: none;
        }
        
        .text-hover-inner {
            pointer-events: auto;
            position: relative;
            z-index: -5;
            isolation: isolate;
        }
        
        .text-hover-svg {
            width: 400px;
            height: 200px;
            cursor: pointer;
        }
        
        .footer {
            position: relative;
            z-index: 10;
            background: rgba(0, 0, 0, 0.8);
            padding: 40px;
            border-top: 1px solid rgba(139, 92, 246, 0.2);
        }
        
        .footer-content {
            position: relative;
            z-index: 20;
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .footer-link {
            position: relative;
            z-index: 30;
            color: rgba(255, 255, 255, 0.8);
            text-decoration: none;
            padding: 8px 16px;
            display: inline-block;
            border: 1px solid rgba(255, 255, 255, 0.2);
            margin: 4px;
            border-radius: 4px;
            transition: all 0.3s ease;
        }
        
        .footer-link:hover {
            color: white;
            background: rgba(139, 92, 246, 0.2);
            border-color: rgba(139, 92, 246, 0.5);
        }
        
        .content {
            height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-direction: column;
        }
        
        .test-result {
            margin-top: 20px;
            padding: 10px;
            border: 1px solid #333;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <div class="content">
        <h1>Z-Index Layering Test</h1>
        <p>Scroll down to test footer links and text hover effect</p>
        <div class="test-result">
            <p>Text Hover Clicks: <span id="textClicks">0</span></p>
            <p>Footer Link Clicks: <span id="footerClicks">0</span></p>
        </div>
    </div>
    
    <!-- Text Hover Effect (Background) -->
    <div class="text-hover-effect">
        <div class="text-hover-inner">
            <svg class="text-hover-svg" viewBox="0 0 300 100" xmlns="http://www.w3.org/2000/svg">
                <text x="50%" y="50%" text-anchor="middle" dominant-baseline="middle" 
                      fill="rgba(255,255,255,0.1)" font-size="24" font-weight="bold">
                    INNOHUB
                </text>
            </svg>
        </div>
    </div>
    
    <!-- Footer -->
    <footer class="footer">
        <div class="footer-content">
            <h3>Footer Links Test</h3>
            <div>
                <a href="#" class="footer-link" onclick="incrementFooterClicks(event)">About Us</a>
                <a href="#" class="footer-link" onclick="incrementFooterClicks(event)">Our Team</a>
                <a href="#" class="footer-link" onclick="incrementFooterClicks(event)">Mentors</a>
                <a href="#" class="footer-link" onclick="incrementFooterClicks(event)">Privacy Policy</a>
                <a href="#" class="footer-link" onclick="incrementFooterClicks(event)">Terms of Service</a>
            </div>
        </div>
    </footer>
    
    <script>
        let textClicks = 0;
        let footerClicks = 0;
        
        // Add click listener to text hover effect
        document.querySelector('.text-hover-svg').addEventListener('click', function(e) {
            textClicks++;
            document.getElementById('textClicks').textContent = textClicks;
            console.log('Text hover clicked');
        });
        
        function incrementFooterClicks(e) {
            e.preventDefault();
            footerClicks++;
            document.getElementById('footerClicks').textContent = footerClicks;
            console.log('Footer link clicked');
        }
        
        // Test if both can work simultaneously
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Test page loaded. Try clicking both the INNOHUB text and footer links.');
        });
    </script>
</body>
</html>
