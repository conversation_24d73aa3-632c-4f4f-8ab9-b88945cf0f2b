'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { 
  Plus, 
  Search, 
  Edit, 
  Trash2, 
  Eye, 
  Calendar,
  User,
  Filter,
  FileText
} from 'lucide-react';
import Link from 'next/link';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

interface BlogPost {
  id: string;
  title: string;
  excerpt?: string;
  content: string;
  is_published: boolean;
  is_featured: boolean;
  view_count: number;
  tags?: string[];
  published_at?: string;
  created_at: string;
  updated_at: string;
  author?: {
    name: string;
    email: string;
  };
}

export default function AdminBlogsPage() {
  const [blogs, setBlogs] = useState<BlogPost[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const router = useRouter();

  useEffect(() => {
    checkAdminAccess();
    loadBlogs();
  }, []);

  const checkAdminAccess = async () => {
    try {
      const response = await fetch('/api/auth/check-admin');
      const result = await response.json();
      
      if (!result.isAdmin) {
        router.push('/admin/login');
        return;
      }
    } catch (error) {
      router.push('/admin/login');
    }
  };

  const loadBlogs = async () => {
    try {
      const response = await fetch('/api/admin/blogs');
      const data = await response.json();
      setBlogs(data.blogs || []);
    } catch (error) {
      console.error('Failed to load blogs:', error);
    } finally {
      setLoading(false);
    }
  };

  const deleteBlog = async (blogId: string) => {
    if (!confirm('Are you sure you want to delete this blog post?')) return;

    try {
      const response = await fetch(`/api/admin/blogs/${blogId}`, {
        method: 'DELETE',
      });

      if (response.ok) {
        setBlogs(blogs.filter(blog => blog.id !== blogId));
      } else {
        alert('Failed to delete blog post');
      }
    } catch (error) {
      console.error('Failed to delete blog:', error);
      alert('Failed to delete blog post');
    }
  };

  const togglePublishStatus = async (blogId: string, currentStatus: boolean) => {
    try {
      const response = await fetch(`/api/admin/blogs/${blogId}`, {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ 
          is_published: !currentStatus,
          published_at: !currentStatus ? new Date().toISOString() : null
        }),
      });

      if (response.ok) {
        setBlogs(blogs.map(blog => 
          blog.id === blogId 
            ? { 
                ...blog, 
                is_published: !currentStatus,
                published_at: !currentStatus ? new Date().toISOString() : undefined
              }
            : blog
        ));
      }
    } catch (error) {
      console.error('Failed to update blog status:', error);
    }
  };

  const filteredBlogs = blogs.filter(blog => {
    const matchesSearch = blog.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         blog.content.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = statusFilter === 'all' || 
                         (statusFilter === 'published' && blog.is_published) ||
                         (statusFilter === 'draft' && !blog.is_published) ||
                         (statusFilter === 'featured' && blog.is_featured);
    
    return matchesSearch && matchesStatus;
  });

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-purple-900 via-black to-purple-900 flex items-center justify-center">
        <div className="text-white">Loading blog posts...</div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-900 via-black to-purple-900">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="flex justify-between items-center mb-8">
          <div>
            <h1 className="text-3xl font-bold text-white">Blog Management</h1>
            <p className="text-gray-400">Create and manage your blog posts</p>
          </div>
          <div className="flex gap-4">
            <Button asChild className="bg-purple-600 hover:bg-purple-700">
              <Link href="/admin/blogs/new">
                <Plus className="w-4 h-4 mr-2" />
                New Blog Post
              </Link>
            </Button>
            <Button asChild variant="outline" className="border-purple-500/20 text-white">
              <Link href="/admin/dashboard">
                Back to Dashboard
              </Link>
            </Button>
          </div>
        </div>

        {/* Filters */}
        <Card className="bg-black/50 border-purple-500/20 backdrop-blur-xl mb-6">
          <CardContent className="pt-6">
            <div className="flex flex-col md:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                  <Input
                    placeholder="Search blog posts..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10 bg-black/50 border-purple-500/20 text-white"
                  />
                </div>
              </div>
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger className="w-[180px] bg-black/50 border-purple-500/20 text-white">
                  <SelectValue placeholder="Status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Posts</SelectItem>
                  <SelectItem value="published">Published</SelectItem>
                  <SelectItem value="draft">Drafts</SelectItem>
                  <SelectItem value="featured">Featured</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </CardContent>
        </Card>

        {/* Blog Posts List */}
        <div className="space-y-4">
          {filteredBlogs.map((blog) => (
            <Card key={blog.id} className="bg-black/50 border-purple-500/20 backdrop-blur-xl">
              <CardContent className="p-6">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-2">
                      <h3 className="text-xl font-semibold text-white">{blog.title}</h3>
                      <div className="flex gap-2">
                        <Badge variant={blog.is_published ? "default" : "secondary"}>
                          {blog.is_published ? "Published" : "Draft"}
                        </Badge>
                        {blog.is_featured && (
                          <Badge variant="outline" className="border-yellow-500 text-yellow-400">
                            Featured
                          </Badge>
                        )}
                      </div>
                    </div>
                    
                    {blog.excerpt && (
                      <p className="text-gray-400 mb-3 line-clamp-2">{blog.excerpt}</p>
                    )}
                    
                    <div className="flex items-center gap-6 text-sm text-gray-500">
                      <div className="flex items-center gap-1">
                        <User className="w-4 h-4" />
                        <span>{blog.author?.name || 'Unknown Author'}</span>
                      </div>
                      <div className="flex items-center gap-1">
                        <Calendar className="w-4 h-4" />
                        <span>
                          {blog.published_at 
                            ? new Date(blog.published_at).toLocaleDateString()
                            : new Date(blog.created_at).toLocaleDateString()
                          }
                        </span>
                      </div>
                      <div className="flex items-center gap-1">
                        <Eye className="w-4 h-4" />
                        <span>{blog.view_count} views</span>
                      </div>
                    </div>

                    {blog.tags && blog.tags.length > 0 && (
                      <div className="flex flex-wrap gap-1 mt-3">
                        {blog.tags.map((tag, index) => (
                          <Badge key={index} variant="outline" className="text-xs">
                            {tag}
                          </Badge>
                        ))}
                      </div>
                    )}
                  </div>
                  
                  <div className="flex gap-2 ml-4">
                    <Button asChild size="sm" variant="outline">
                      <Link href={`/admin/blogs/${blog.id}`}>
                        <Edit className="w-4 h-4 mr-1" />
                        Edit
                      </Link>
                    </Button>
                    <Button 
                      size="sm" 
                      variant="outline"
                      onClick={() => togglePublishStatus(blog.id, blog.is_published)}
                    >
                      <Eye className="w-4 h-4 mr-1" />
                      {blog.is_published ? 'Unpublish' : 'Publish'}
                    </Button>
                    <Button 
                      size="sm" 
                      variant="destructive"
                      onClick={() => deleteBlog(blog.id)}
                    >
                      <Trash2 className="w-4 h-4" />
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {filteredBlogs.length === 0 && (
          <Card className="bg-black/50 border-purple-500/20 backdrop-blur-xl">
            <CardContent className="text-center py-12">
              <FileText className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-white mb-2">No blog posts found</h3>
              <p className="text-gray-400 mb-4">
                {searchTerm || statusFilter !== 'all' 
                  ? 'Try adjusting your filters' 
                  : 'Get started by creating your first blog post'}
              </p>
              <Button asChild className="bg-purple-600 hover:bg-purple-700">
                <Link href="/admin/blogs/new">
                  <Plus className="w-4 h-4 mr-2" />
                  Create Blog Post
                </Link>
              </Button>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
}
