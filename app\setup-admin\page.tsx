'use client';

import { useState } from 'react';
import { motion } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Shield, User, Mail, Lock, CheckCircle } from 'lucide-react';
import { appwriteUserService } from '@/lib/appwrite/users';

export default function SetupAdminPage() {
  const [formData, setFormData] = useState({
    name: 'Admin User',
    email: '<EMAIL>',
    password: 'admin123456'
  });
  const [isLoading, setIsLoading] = useState(false);
  const [success, setSuccess] = useState(false);
  const [error, setError] = useState('');

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError('');
    setSuccess(false);

    try {
      await appwriteUserService.createAdminUser({
        email: formData.email,
        password: formData.password,
        name: formData.name,
        role: 'ADMIN'
      });

      setSuccess(true);
    } catch (error: any) {
      console.error('Admin creation error:', error);
      if (error.message.includes('user_already_exists')) {
        setError('Admin user already exists with this email');
      } else {
        setError(error.message || 'Failed to create admin user');
      }
    } finally {
      setIsLoading(false);
    }
  };

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  if (success) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-purple-900 via-black to-purple-900 flex items-center justify-center p-4">
        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          className="w-full max-w-md"
        >
          <Card className="border-green-500/20 bg-black/60 backdrop-blur-md">
            <CardHeader className="text-center space-y-4">
              <div className="mx-auto w-16 h-16 bg-green-500/20 rounded-full flex items-center justify-center">
                <CheckCircle className="h-8 w-8 text-green-400" />
              </div>
              <div>
                <CardTitle className="text-2xl font-bold text-white">Admin User Created!</CardTitle>
                <p className="text-gray-400 mt-2">Your admin account has been set up successfully</p>
              </div>
            </CardHeader>

            <CardContent className="space-y-6">
              <div className="bg-green-500/10 border border-green-500/20 rounded-lg p-4 text-green-400 text-sm">
                <strong>Admin Credentials:</strong><br />
                <div className="mt-2 font-mono">
                  Email: <span className="text-white">{formData.email}</span><br />
                  Password: <span className="text-white">{formData.password}</span>
                </div>
              </div>

              <div className="space-y-3">
                <Button
                  onClick={() => window.location.href = '/auth/admin'}
                  className="w-full bg-purple-600 hover:bg-purple-700 text-white"
                >
                  Go to Admin Login
                </Button>
                
                <Button
                  onClick={() => window.location.href = '/'}
                  variant="outline"
                  className="w-full border-gray-600 text-gray-300 hover:bg-gray-800"
                >
                  Back to Home
                </Button>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-900 via-black to-purple-900 flex items-center justify-center p-4">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="w-full max-w-md"
      >
        <Card className="border-purple-500/20 bg-black/60 backdrop-blur-md">
          <CardHeader className="text-center space-y-4">
            <div className="mx-auto w-16 h-16 bg-purple-500/20 rounded-full flex items-center justify-center">
              <Shield className="h-8 w-8 text-purple-400" />
            </div>
            <div>
              <CardTitle className="text-2xl font-bold text-white">Setup Admin User</CardTitle>
              <p className="text-gray-400 mt-2">Create the first admin account for InnoHub</p>
            </div>
          </CardHeader>

          <CardContent className="space-y-6">
            <form onSubmit={handleSubmit} className="space-y-4">
              {error && (
                <Alert className="border-red-500/20 bg-red-500/10">
                  <AlertDescription className="text-red-400">
                    {error}
                  </AlertDescription>
                </Alert>
              )}

              <div className="bg-blue-500/10 border border-blue-500/20 rounded-lg p-4 text-blue-400 text-sm">
                <strong>Note:</strong> This will create an admin user in your Appwrite database. 
                Make sure you have created the 'users' collection first.
              </div>

              <div className="space-y-2">
                <Label htmlFor="name" className="text-gray-300">Admin Name</Label>
                <div className="relative">
                  <User className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                  <Input
                    id="name"
                    type="text"
                    value={formData.name}
                    onChange={(e) => handleInputChange('name', e.target.value)}
                    className="pl-10 bg-gray-800/50 border-gray-600 text-white placeholder-gray-400 focus:border-purple-500"
                    placeholder="Admin User"
                    required
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="email" className="text-gray-300">Admin Email</Label>
                <div className="relative">
                  <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                  <Input
                    id="email"
                    type="email"
                    value={formData.email}
                    onChange={(e) => handleInputChange('email', e.target.value)}
                    className="pl-10 bg-gray-800/50 border-gray-600 text-white placeholder-gray-400 focus:border-purple-500"
                    placeholder="<EMAIL>"
                    required
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="password" className="text-gray-300">Admin Password</Label>
                <div className="relative">
                  <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                  <Input
                    id="password"
                    type="text"
                    value={formData.password}
                    onChange={(e) => handleInputChange('password', e.target.value)}
                    className="pl-10 bg-gray-800/50 border-gray-600 text-white placeholder-gray-400 focus:border-purple-500"
                    placeholder="admin123456"
                    required
                  />
                </div>
                <p className="text-xs text-gray-500">Password must be at least 8 characters long</p>
              </div>

              <Button
                type="submit"
                disabled={isLoading}
                className="w-full bg-purple-600 hover:bg-purple-700 disabled:opacity-50 text-white font-medium"
              >
                {isLoading ? 'Creating Admin User...' : 'Create Admin User'}
              </Button>
            </form>

            <div className="text-center">
              <button
                onClick={() => window.location.href = '/'}
                className="text-gray-400 hover:text-white text-sm"
              >
                ← Back to Home
              </button>
            </div>
          </CardContent>
        </Card>
      </motion.div>
    </div>
  );
}
