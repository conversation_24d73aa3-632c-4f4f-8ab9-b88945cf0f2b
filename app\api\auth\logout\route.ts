import { NextRequest, NextResponse } from 'next/server';
import { createSupabaseServerClient } from '@/lib/supabase/client';

export async function POST(request: NextRequest) {
  try {
    const serverClient = createSupabaseServerClient();
    
    // Sign out the user
    await serverClient.auth.signOut();
    
    return NextResponse.json({ success: true });
  } catch (error: any) {
    console.error('Logout error:', error);
    return NextResponse.json({ success: true }); // Always return success for logout
  }
}
