"use client"

import React, { useEffect, useState } from "react"
import { motion } from "framer-motion"
import Link from "next/link"
import { LucideIcon, GraduationCap, Globe, Menu } from "lucide-react"
import { cn } from "@/lib/utils"
import { usePathname } from "next/navigation"
import Image from "next/image"
import { useLanguage } from "@/lib/context/language-context"
import { HoverBorderGradient } from "@/components/ui/hover-border-gradient"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from "@/components/ui/dropdown-menu"
import { Sheet, SheetContent, SheetTrigger } from "@/components/ui/sheet"

interface NavItem {
  name: string
  url: string
  icon: LucideIcon
}

interface NavBarProps {
  items: NavItem[]
  className?: string
}

export function NavBar({ items, className }: NavBarProps) {
  const [activeTab, setActiveTab] = useState(items[0]?.name || '')
  const [isMobile, setIsMobile] = useState(false)
  const [isSheetOpen, setIsSheetOpen] = useState(false)
  const pathname = usePathname()
  const { t, language, setLanguage, isLoaded } = useLanguage()

  useEffect(() => {
    const handleResize = () => {
      setIsMobile(window.innerWidth < 768)
    }

    handleResize()
    window.addEventListener("resize", handleResize)
    return () => window.removeEventListener("resize", handleResize)
  }, [])

  // Update active tab based on current pathname
  useEffect(() => {
    const currentItem = items.find(item => item.url === pathname)
    if (currentItem) {
      setActiveTab(currentItem.name)
    }
  }, [pathname, items])

  return (
    <div
      className={cn(
        "fixed top-0 left-0 right-0 z-50 bg-black/20 backdrop-blur-md border-b border-purple-500/20",
        className,
      )}
    >
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4 flex items-center justify-between">
        {/* Logo (Left) */}
        <Link href="/" className="flex items-center gap-2 z-10">
          <div className="relative w-10 h-10 rounded-full overflow-hidden">
            <Image
              src="/images/logo/innohub_logo.png"
              alt="InnoHub Logo"
              width={40}
              height={40}
              className="object-cover"
            />
          </div>
          <span className="font-bold text-xl text-white">INNO<span className="text-purple-500">HUB</span></span>
        </Link>

        {/* Desktop Navigation (Center) */}
        <nav className="hidden md:flex items-center absolute left-1/2 transform -translate-x-1/2">
          <div className="flex items-center gap-1 bg-black/20 border border-purple-500/30 backdrop-blur-lg py-1 px-1 rounded-full shadow-lg">
            {items?.map((item) => {
              const Icon = item.icon
              const isActive = activeTab === item.name

              return (
                <Link
                  key={item.name}
                  href={item.url}
                  onClick={() => setActiveTab(item.name)}
                  className={cn(
                    "relative cursor-pointer text-sm font-semibold px-4 py-2 rounded-full transition-all duration-300",
                    "text-white/80 hover:text-white",
                    isActive && "text-white",
                  )}
                >
                  <span className="relative z-10">{item.name}</span>
                  {isActive && (
                    <motion.div
                      layoutId="tubelight"
                      className="absolute inset-0 w-full bg-purple-600/20 rounded-full -z-10"
                      initial={false}
                      transition={{
                        type: "spring",
                        stiffness: 300,
                        damping: 30,
                      }}
                    >
                      {/* Tubelight effect */}
                      <div className="absolute -top-2 left-1/2 -translate-x-1/2 w-8 h-1 bg-purple-500 rounded-t-full">
                        <div className="absolute w-12 h-6 bg-purple-500/30 rounded-full blur-md -top-2 -left-2" />
                        <div className="absolute w-8 h-6 bg-purple-500/40 rounded-full blur-md -top-1" />
                        <div className="absolute w-4 h-4 bg-purple-500/50 rounded-full blur-sm top-0 left-2" />
                      </div>
                    </motion.div>
                  )}
                </Link>
              )
            })}
          </div>
        </nav>

        {/* Right Side - Courses Button and Language Switcher */}
        <div className="hidden md:flex items-center gap-4">
          {/* Language Switcher */}
          {isLoaded && (
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <HoverBorderGradient
                  containerClassName="rounded-full h-[2.25rem] w-[3rem]"
                  className="dark:bg-slate-950 bg-slate-950 text-white flex items-center justify-center gap-1 h-full w-full px-2"
                  duration={2}
                >
                  <Globe className="h-4 w-4" />
                  <span className="text-sm">{language === 'en' ? '🇺🇸' : '🇲🇳'}</span>
                  <span className="sr-only">{t('nav.language')}</span>
                </HoverBorderGradient>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="bg-black/90 backdrop-blur-md border border-purple-500 rounded-xl">
                <DropdownMenuItem
                  className={`${language === 'en' ? 'bg-purple-700/30' : ''} text-white hover:bg-black/70 rounded-lg my-1 flex items-center gap-2`}
                  onClick={() => setLanguage('en')}
                >
                  <span>🇺🇸</span>
                  {t('nav.english')}
                </DropdownMenuItem>
                <DropdownMenuItem
                  className={`${language === 'mn' ? 'bg-purple-700/30' : ''} text-white hover:bg-black/70 rounded-lg my-1 flex items-center gap-2`}
                  onClick={() => setLanguage('mn')}
                >
                  <span>🇲🇳</span>
                  {t('nav.mongolian')}
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          )}

          {/* Courses Button */}
          <Link
            href="/courses"
            className={cn(
              "relative cursor-pointer text-sm font-semibold px-6 py-2 rounded-full transition-all duration-300",
              "text-white/80 hover:text-white border border-purple-500/30 bg-purple-600/10 hover:bg-purple-600/20",
              pathname === '/courses' && "text-white bg-purple-600/30 border-purple-500",
            )}
          >
            <div className="flex items-center gap-2">
              <GraduationCap size={16} />
              <span>Courses</span>
            </div>
            {pathname === '/courses' && (
              <motion.div
                layoutId="courses-highlight"
                className="absolute inset-0 w-full bg-purple-600/20 rounded-full -z-10"
                initial={false}
                transition={{
                  type: "spring",
                  stiffness: 300,
                  damping: 30,
                }}
              >
                {/* Tubelight effect for courses */}
                <div className="absolute -top-2 left-1/2 -translate-x-1/2 w-8 h-1 bg-purple-500 rounded-t-full">
                  <div className="absolute w-12 h-6 bg-purple-500/30 rounded-full blur-md -top-2 -left-2" />
                  <div className="absolute w-8 h-6 bg-purple-500/40 rounded-full blur-md -top-1" />
                  <div className="absolute w-4 h-4 bg-purple-500/50 rounded-full blur-sm top-0 left-2" />
                </div>
              </motion.div>
            )}
          </Link>
        </div>

        {/* Mobile Menu */}
        <div className="md:hidden flex items-center gap-3">
          {/* Mobile Language Switcher */}
          {isLoaded && (
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <button className="text-white p-2 rounded-full bg-purple-600/10 border border-purple-500/30">
                  <Globe className="h-5 w-5" />
                </button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="bg-black/90 backdrop-blur-md border border-purple-500 rounded-xl">
                <DropdownMenuItem
                  className={`${language === 'en' ? 'bg-purple-700/30' : ''} text-white hover:bg-black/70 rounded-lg my-1 flex items-center gap-2`}
                  onClick={() => setLanguage('en')}
                >
                  <span>🇺🇸</span>
                  {t('nav.english')}
                </DropdownMenuItem>
                <DropdownMenuItem
                  className={`${language === 'mn' ? 'bg-purple-700/30' : ''} text-white hover:bg-black/70 rounded-lg my-1 flex items-center gap-2`}
                  onClick={() => setLanguage('mn')}
                >
                  <span>🇲🇳</span>
                  {t('nav.mongolian')}
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          )}

          {/* Mobile Menu Button */}
          <Sheet open={isSheetOpen} onOpenChange={setIsSheetOpen}>
            <SheetTrigger asChild>
              <button className="text-white p-2">
                <Menu className="h-6 w-6" />
              </button>
            </SheetTrigger>
            <SheetContent side="right" className="w-[300px] bg-black/95 backdrop-blur-md border-l border-purple-500/20">
              <div className="flex flex-col h-full">
                {/* Mobile Navigation Links */}
                <div className="flex flex-col gap-4 mt-8">
                  {items?.map((item) => {
                    const Icon = item.icon
                    const isActive = activeTab === item.name

                    return (
                      <Link
                        key={item.name}
                        href={item.url}
                        onClick={() => {
                          setActiveTab(item.name)
                          setIsSheetOpen(false)
                        }}
                        className={cn(
                          "flex items-center gap-3 px-4 py-3 rounded-xl transition-all duration-300",
                          "text-white/80 hover:text-white hover:bg-purple-600/20",
                          isActive && "text-white bg-purple-600/30 border border-purple-500/50",
                        )}
                      >
                        <Icon size={20} />
                        <span className="font-medium">{item.name}</span>
                      </Link>
                    )
                  })}

                  {/* Mobile Courses Link */}
                  <Link
                    href="/courses"
                    onClick={() => setIsSheetOpen(false)}
                    className={cn(
                      "flex items-center gap-3 px-4 py-3 rounded-xl transition-all duration-300",
                      "text-white/80 hover:text-white hover:bg-purple-600/20",
                      pathname === '/courses' && "text-white bg-purple-600/30 border border-purple-500/50",
                    )}
                  >
                    <GraduationCap size={20} />
                    <span className="font-medium">Courses</span>
                  </Link>
                </div>

                {/* Mobile Language Switcher Section */}
                {isLoaded && (
                  <div className="mt-auto p-6 border-t border-purple-500/20">
                    <p className="text-white/60 text-sm mb-3">{t('nav.language')}:</p>
                    <div className="flex gap-3">
                      <motion.button
                        onClick={() => setLanguage('en')}
                        className={cn(
                          "flex items-center gap-2 px-4 py-3 rounded-xl transition-all duration-300 min-h-[48px] flex-1",
                          language === 'en'
                            ? 'bg-purple-500/30 text-white border border-purple-500/50'
                            : 'bg-white/5 text-white/80 hover:bg-white/10'
                        )}
                        whileTap={{ scale: 0.95 }}
                      >
                        <span className="text-base">🇺🇸</span>
                        <span className="text-sm font-medium">{t('nav.english')}</span>
                      </motion.button>
                      <motion.button
                        onClick={() => setLanguage('mn')}
                        className={cn(
                          "flex items-center gap-2 px-4 py-3 rounded-xl transition-all duration-300 min-h-[48px] flex-1",
                          language === 'mn'
                            ? 'bg-purple-500/30 text-white border border-purple-500/50'
                            : 'bg-white/5 text-white/80 hover:bg-white/10'
                        )}
                        whileTap={{ scale: 0.95 }}
                      >
                        <span className="text-base">🇲🇳</span>
                        <span className="text-sm font-medium">{t('nav.mongolian')}</span>
                      </motion.button>
                    </div>
                  </div>
                )}
              </div>
            </SheetContent>
          </Sheet>
        </div>
      </div>
    </div>
  )
}
