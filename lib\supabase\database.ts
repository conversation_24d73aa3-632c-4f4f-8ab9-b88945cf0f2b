import { createSupabaseClient, createSupabaseServerClient } from './client';
import type { Database } from './types';

type Course = Database['public']['Tables']['courses']['Row'];
type CourseInsert = Database['public']['Tables']['courses']['Insert'];
type CourseUpdate = Database['public']['Tables']['courses']['Update'];

type BlogPost = Database['public']['Tables']['blog_posts']['Row'];
type BlogPostInsert = Database['public']['Tables']['blog_posts']['Insert'];
type BlogPostUpdate = Database['public']['Tables']['blog_posts']['Update'];

type Enrollment = Database['public']['Tables']['enrollments']['Row'];
type EnrollmentInsert = Database['public']['Tables']['enrollments']['Insert'];

type Payment = Database['public']['Tables']['payments']['Row'];
type PaymentInsert = Database['public']['Tables']['payments']['Insert'];
type PaymentUpdate = Database['public']['Tables']['payments']['Update'];

export class SupabaseDatabaseService {
  private supabase = createSupabaseClient();

  // Course methods
  async getCourses(): Promise<{ data: Course[] | null; error: string | null }> {
    try {
      const { data, error } = await this.supabase
        .from('courses')
        .select('*')
        .eq('is_published', true)
        .order('created_at', { ascending: false });

      return { data, error: error?.message || null };
    } catch (error: any) {
      return { data: null, error: error.message };
    }
  }

  async getCourse(id: string): Promise<{ data: Course | null; error: string | null }> {
    try {
      const { data, error } = await this.supabase
        .from('courses')
        .select('*')
        .eq('id', id)
        .single();

      return { data, error: error?.message || null };
    } catch (error: any) {
      return { data: null, error: error.message };
    }
  }

  async createCourse(course: CourseInsert): Promise<{ data: Course | null; error: string | null }> {
    try {
      const { data, error } = await this.supabase
        .from('courses')
        .insert(course)
        .select()
        .single();

      return { data, error: error?.message || null };
    } catch (error: any) {
      return { data: null, error: error.message };
    }
  }

  async updateCourse(id: string, updates: CourseUpdate): Promise<{ data: Course | null; error: string | null }> {
    try {
      const { data, error } = await this.supabase
        .from('courses')
        .update({ ...updates, updated_at: new Date().toISOString() })
        .eq('id', id)
        .select()
        .single();

      return { data, error: error?.message || null };
    } catch (error: any) {
      return { data: null, error: error.message };
    }
  }

  // Blog methods
  async getBlogPosts(): Promise<{ data: BlogPost[] | null; error: string | null }> {
    try {
      const { data, error } = await this.supabase
        .from('blog_posts')
        .select('*')
        .eq('is_published', true)
        .order('created_at', { ascending: false });

      return { data, error: error?.message || null };
    } catch (error: any) {
      return { data: null, error: error.message };
    }
  }

  async getBlogPost(id: string): Promise<{ data: BlogPost | null; error: string | null }> {
    try {
      const { data, error } = await this.supabase
        .from('blog_posts')
        .select('*')
        .eq('id', id)
        .single();

      return { data, error: error?.message || null };
    } catch (error: any) {
      return { data: null, error: error.message };
    }
  }

  async createBlogPost(post: BlogPostInsert): Promise<{ data: BlogPost | null; error: string | null }> {
    try {
      const { data, error } = await this.supabase
        .from('blog_posts')
        .insert(post)
        .select()
        .single();

      return { data, error: error?.message || null };
    } catch (error: any) {
      return { data: null, error: error.message };
    }
  }

  // Enrollment methods
  async enrollInCourse(userId: string, courseId: string): Promise<{ data: Enrollment | null; error: string | null }> {
    try {
      // Check if already enrolled
      const { data: existing } = await this.supabase
        .from('enrollments')
        .select('id')
        .eq('user_id', userId)
        .eq('course_id', courseId)
        .single();

      if (existing) {
        return { data: null, error: 'Already enrolled in this course' };
      }

      const { data, error } = await this.supabase
        .from('enrollments')
        .insert({
          user_id: userId,
          course_id: courseId,
          progress: 0
        })
        .select()
        .single();

      return { data, error: error?.message || null };
    } catch (error: any) {
      return { data: null, error: error.message };
    }
  }

  async getUserEnrollments(userId: string): Promise<{ data: (Enrollment & { course: Course })[] | null; error: string | null }> {
    try {
      const { data, error } = await this.supabase
        .from('enrollments')
        .select(`
          *,
          course:courses(*)
        `)
        .eq('user_id', userId)
        .order('enrolled_at', { ascending: false });

      return { data: data as any, error: error?.message || null };
    } catch (error: any) {
      return { data: null, error: error.message };
    }
  }

  async isEnrolled(userId: string, courseId: string): Promise<boolean> {
    try {
      const { data } = await this.supabase
        .from('enrollments')
        .select('id')
        .eq('user_id', userId)
        .eq('course_id', courseId)
        .single();

      return !!data;
    } catch {
      return false;
    }
  }

  // Payment methods
  async createPayment(payment: PaymentInsert): Promise<{ data: Payment | null; error: string | null }> {
    try {
      const { data, error } = await this.supabase
        .from('payments')
        .insert(payment)
        .select()
        .single();

      return { data, error: error?.message || null };
    } catch (error: any) {
      return { data: null, error: error.message };
    }
  }

  async updatePayment(id: string, updates: PaymentUpdate): Promise<{ data: Payment | null; error: string | null }> {
    try {
      const { data, error } = await this.supabase
        .from('payments')
        .update({ ...updates, updated_at: new Date().toISOString() })
        .eq('id', id)
        .select()
        .single();

      return { data, error: error?.message || null };
    } catch (error: any) {
      return { data: null, error: error.message };
    }
  }

  async getPayment(transactionId: string): Promise<{ data: Payment | null; error: string | null }> {
    try {
      const { data, error } = await this.supabase
        .from('payments')
        .select('*')
        .eq('transaction_id', transactionId)
        .single();

      return { data, error: error?.message || null };
    } catch (error: any) {
      return { data: null, error: error.message };
    }
  }

  async getUserPayments(userId: string): Promise<{ data: Payment[] | null; error: string | null }> {
    try {
      const { data, error } = await this.supabase
        .from('payments')
        .select('*')
        .eq('user_id', userId)
        .order('created_at', { ascending: false });

      return { data, error: error?.message || null };
    } catch (error: any) {
      return { data: null, error: error.message };
    }
  }

  // Analytics methods
  async getStats(): Promise<{ 
    totalUsers: number; 
    totalCourses: number; 
    totalEnrollments: number; 
    totalRevenue: number;
    error: string | null;
  }> {
    try {
      const [usersResult, coursesResult, enrollmentsResult, paymentsResult] = await Promise.all([
        this.supabase.from('users').select('id', { count: 'exact', head: true }),
        this.supabase.from('courses').select('id', { count: 'exact', head: true }),
        this.supabase.from('enrollments').select('id', { count: 'exact', head: true }),
        this.supabase.from('payments').select('amount').eq('status', 'completed')
      ]);

      const totalRevenue = paymentsResult.data?.reduce((sum, payment) => sum + payment.amount, 0) || 0;

      return {
        totalUsers: usersResult.count || 0,
        totalCourses: coursesResult.count || 0,
        totalEnrollments: enrollmentsResult.count || 0,
        totalRevenue,
        error: null
      };
    } catch (error: any) {
      return {
        totalUsers: 0,
        totalCourses: 0,
        totalEnrollments: 0,
        totalRevenue: 0,
        error: error.message
      };
    }
  }
}

export const databaseService = new SupabaseDatabaseService();
