import { NextRequest, NextResponse } from 'next/server';
import { createSupabaseServerClient } from '@/lib/supabase/client';

export async function POST(request: NextRequest) {
  try {
    const { email, password } = await request.json();
    console.log('Admin login attempt:', { email, passwordLength: password?.length });

    if (!email || !password) {
      return NextResponse.json(
        { success: false, error: 'Email and password are required' },
        { status: 400 }
      );
    }

    const serverClient = createSupabaseServerClient();

    // First check if user exists in our database
    console.log('Checking user in database...');
    const { data: dbUser, error: dbError } = await serverClient
      .from('users')
      .select('id, email, role')
      .eq('email', email)
      .single();

    console.log('Database user check:', { found: !!dbUser, role: dbUser?.role, error: dbError?.message });

    if (!dbUser) {
      return NextResponse.json(
        { success: false, error: 'User not found in database' },
        { status: 404 }
      );
    }

    if (dbUser.role !== 'ADMIN') {
      return NextResponse.json(
        { success: false, error: 'Access denied. Admin privileges required.' },
        { status: 403 }
      );
    }

    // Try to sign in with Supabase Auth
    console.log('Attempting Supabase auth...');
    const { data: authData, error: authError } = await serverClient.auth.signInWithPassword({
      email,
      password
    });

    console.log('Auth result:', {
      success: !!authData.user,
      error: authError?.message,
      userId: authData.user?.id
    });

    if (authError || !authData.user) {
      console.error('Auth failed:', authError);

      // If auth user exists but password is wrong, reset the password
      if (authError?.message?.includes('Invalid login credentials')) {
        console.log('Auth user exists but password is wrong. Resetting password...');

        // Get all auth users to find the one with this email
        const { data: authUsers, error: listError } = await serverClient.auth.admin.listUsers();

        if (listError) {
          console.error('Failed to list users:', listError);
          return NextResponse.json(
            { success: false, error: `Failed to list users: ${listError.message}` },
            { status: 500 }
          );
        }

        const existingAuthUser = authUsers.users.find(u => u.email === email);

        if (existingAuthUser) {
          console.log('Found existing auth user, updating password...');

          // Update the password for the existing auth user
          const { data: updateData, error: updateError } = await serverClient.auth.admin.updateUserById(
            existingAuthUser.id,
            { password }
          );

          if (updateError) {
            console.error('Failed to update password:', updateError);
            return NextResponse.json(
              { success: false, error: `Failed to update password: ${updateError.message}` },
              { status: 500 }
            );
          }

          console.log('Password updated successfully');

          // Update our database user with the correct auth ID
          await serverClient
            .from('users')
            .update({ id: existingAuthUser.id })
            .eq('email', email);

          console.log('Database user ID updated');

          // Now try to sign in again with the updated password
          const { data: retryAuthData, error: retryError } = await serverClient.auth.signInWithPassword({
            email,
            password
          });

          if (retryError || !retryAuthData.user) {
            console.error('Login still failed after password update:', retryError);
            return NextResponse.json(
              { success: false, error: `Login still failed: ${retryError?.message}` },
              { status: 401 }
            );
          }

          console.log('Login successful after password update');
          // Use the retry auth data
          authData.user = retryAuthData.user;
        } else {
          console.log('No auth user found, creating new one...');
          const { data: newAuthData, error: createError } = await serverClient.auth.admin.createUser({
            email,
            password,
            email_confirm: true,
            user_metadata: {
              name: 'Admin User',
              role: 'ADMIN'
            }
          });

          if (createError) {
            console.error('Failed to create auth user:', createError);
            return NextResponse.json(
              { success: false, error: `Failed to create auth user: ${createError.message}` },
              { status: 500 }
            );
          }

          // Update our database user with the new auth ID
          await serverClient
            .from('users')
            .update({ id: newAuthData.user.id })
            .eq('email', email);

          console.log('New auth user created and database updated');
          authData.user = newAuthData.user;
        }
      } else {
        return NextResponse.json(
          { success: false, error: `Invalid credentials: ${authError?.message || 'Unknown error'}` },
          { status: 401 }
        );
      }
    }

    // Check if user is admin
    const { data: userData, error: userError } = await serverClient
      .from('users')
      .select('id, name, email, role, is_active')
      .eq('id', authData.user.id)
      .single();

    if (userError || !userData) {
      return NextResponse.json(
        { success: false, error: 'User not found' },
        { status: 404 }
      );
    }

    if (userData.role !== 'ADMIN') {
      return NextResponse.json(
        { success: false, error: 'Access denied. Admin privileges required.' },
        { status: 403 }
      );
    }

    if (!userData.is_active) {
      return NextResponse.json(
        { success: false, error: 'Account is deactivated' },
        { status: 403 }
      );
    }

    // Update last login
    await serverClient
      .from('users')
      .update({ last_login: new Date().toISOString() })
      .eq('id', userData.id);

    return NextResponse.json({
      success: true,
      user: {
        id: userData.id,
        name: userData.name,
        email: userData.email,
        role: userData.role
      }
    });

  } catch (error: any) {
    console.error('Admin login error:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}
