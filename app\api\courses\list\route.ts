import { NextRequest, NextResponse } from 'next/server';
import { databaseService } from '@/lib/supabase/database';

export async function GET(request: NextRequest) {
  try {
    const { data: courses, error } = await databaseService.getCourses();

    if (error) {
      return NextResponse.json(
        {
          success: false,
          error: error
        },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      courses: courses?.map(course => ({
        id: course.id,
        title: course.title,
        description: course.description,
        instructor: course.instructor,
        price: course.price,
        isPublished: course.is_published
      })) || []
    });
  } catch (error) {
    console.error('Failed to list courses:', error);
    return NextResponse.json(
      { error: 'Failed to fetch courses' },
      { status: 500 }
    );
  }
}
