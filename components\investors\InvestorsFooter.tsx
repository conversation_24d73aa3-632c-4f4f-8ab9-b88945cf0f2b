'use client';

import Link from 'next/link';
import { motion } from 'framer-motion';
import { useLanguage } from '@/lib/context/language-context';

export default function InvestorsFooter() {
  const { language } = useLanguage();

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
      },
    },
  };

  return (
    <footer className="relative border-t border-primary/20 bg-gradient-to-b from-background to-black overflow-hidden">
      {/* Background effects */}
      <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent z-0" />
      
      {/* Subtle grid pattern */}
      <div className="absolute inset-0 opacity-5 z-0">
        <div className="absolute inset-0" style={{
          backgroundImage: `
            linear-gradient(rgba(139, 92, 246, 0.1) 1px, transparent 1px),
            linear-gradient(90deg, rgba(139, 92, 246, 0.1) 1px, transparent 1px)
          `,
          backgroundSize: '50px 50px'
        }} />
      </div>

      {/* Main content */}
      <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16 z-10">
        {/* Copyright and Oyu Intelligence Credit */}
        <motion.div
          className="flex flex-col md:flex-row justify-between items-center"
          variants={containerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
        >
          <motion.div variants={itemVariants}>
            <p className="text-white/60 text-sm">
              © 2024 InnoHub. {language === 'mn' ? 'Бүх эрх хуулиар хамгаалагдсан.' : 'All rights reserved.'}
            </p>
          </motion.div>

          {/* Oyu Intelligence LLC Credit */}
          <motion.div variants={itemVariants} className="mt-4 md:mt-0">
            <p className="text-white/60 text-sm">
              {language === 'mn' ? 'Хөгжүүлсэн:' : 'Developed by'}{' '}
              <Link 
                href="https://www.oyu-intelligence.com" 
                target="_blank" 
                rel="noopener noreferrer"
                className="text-primary hover:text-primary/80 transition-colors font-medium"
              >
                Oyu Intelligence LLC
              </Link>
            </p>
          </motion.div>
        </motion.div>
      </div>
    </footer>
  );
}
