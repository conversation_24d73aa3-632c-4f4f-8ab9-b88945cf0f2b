'use client';

import { motion } from 'framer-motion';
import Image from 'next/image';
import Link from 'next/link';
import { ArrowLeft } from 'lucide-react';
import { ShootingStars } from '@/components/ui/shooting-stars';
import { StarsBackground } from '@/components/ui/stars-background';

interface AuthLayoutProps {
  children: React.ReactNode;
  title: string;
  subtitle?: string;
  showBackButton?: boolean;
  backHref?: string;
  backText?: string;
}

export default function AuthLayout({
  children,
  title,
  subtitle,
  showBackButton = true,
  backHref = '/',
  backText = 'Back to Home'
}: AuthLayoutProps) {
  return (
    <div className="min-h-screen bg-gradient-to-br from-black via-purple-900/20 to-black flex items-center justify-center p-4 relative overflow-hidden">
      {/* Background Effects */}
      <StarsBackground />
      <ShootingStars />
      
      {/* Main Content */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, ease: "easeOut" }}
        className="w-full max-w-md relative z-10"
      >
        {/* Logo and Title */}
        <div className="text-center mb-8">
          <motion.div
            initial={{ scale: 0.8, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            transition={{ delay: 0.2, duration: 0.5 }}
            className="flex items-center justify-center mb-6"
          >
            <div className="relative w-16 h-16 rounded-full overflow-hidden mr-4 ring-2 ring-purple-500/30">
              <Image
                src="/images/logo/innohub_logo.png"
                alt="InnoHub Logo"
                width={64}
                height={64}
                className="object-cover"
              />
            </div>
            <span className="font-bold text-3xl text-white">
              INNO<span className="text-purple-500">HUB</span>
            </span>
          </motion.div>
          
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4, duration: 0.5 }}
          >
            <h1 className="text-2xl font-bold text-white mb-2">{title}</h1>
            {subtitle && (
              <p className="text-gray-400 text-sm">{subtitle}</p>
            )}
          </motion.div>
        </div>

        {/* Auth Form Container */}
        <motion.div
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ delay: 0.3, duration: 0.5 }}
          className="bg-black/60 backdrop-blur-md border border-purple-500/20 rounded-2xl p-8 shadow-2xl"
        >
          {children}
        </motion.div>

        {/* Back Button */}
        {showBackButton && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.6, duration: 0.5 }}
            className="text-center mt-6"
          >
            <Link 
              href={backHref} 
              className="inline-flex items-center text-gray-400 hover:text-white transition-colors duration-200"
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              {backText}
            </Link>
          </motion.div>
        )}
      </motion.div>
    </div>
  );
}
