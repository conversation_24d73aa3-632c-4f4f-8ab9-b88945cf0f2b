import { NextRequest, NextResponse } from 'next/server';
import { createSupabaseServerClient } from '@/lib/supabase/client';

// Admin authorization codes (in production, store these securely)
const VALID_ADMIN_CODES = [
  'INNOHUB_ADMIN_2024',
  'SUPER_ADMIN_ACCESS',
  'ADMIN_BOOTSTRAP_KEY'
];

export async function POST(request: NextRequest) {
  try {
    const { name, email, password, adminCode } = await request.json();

    console.log('Admin registration attempt:', { name, email, adminCode });

    // Validate required fields
    if (!name || !email || !password || !adminCode) {
      return NextResponse.json(
        { success: false, error: 'All fields are required' },
        { status: 400 }
      );
    }

    // Validate admin code
    if (!VALID_ADMIN_CODES.includes(adminCode)) {
      return NextResponse.json(
        { success: false, error: 'Invalid admin authorization code' },
        { status: 403 }
      );
    }

    // Validate password strength for admin accounts
    if (password.length < 12) {
      return NextResponse.json(
        { success: false, error: 'Admin password must be at least 12 characters long' },
        { status: 400 }
      );
    }

    const serverClient = createSupabaseServerClient();

    // Check if user already exists
    const { data: existingUser } = await serverClient
      .from('users')
      .select('id, email')
      .eq('email', email)
      .single();

    if (existingUser) {
      return NextResponse.json(
        { success: false, error: 'An account with this email already exists' },
        { status: 409 }
      );
    }

    // Create auth user
    const { data: authData, error: authError } = await serverClient.auth.admin.createUser({
      email,
      password,
      email_confirm: true,
      user_metadata: {
        name,
        role: 'ADMIN'
      }
    });

    if (authError) {
      console.error('Auth user creation failed:', authError);
      return NextResponse.json(
        { success: false, error: `Failed to create auth user: ${authError.message}` },
        { status: 500 }
      );
    }

    if (!authData.user) {
      return NextResponse.json(
        { success: false, error: 'Failed to create auth user' },
        { status: 500 }
      );
    }

    // Create user profile in database
    const { error: dbError } = await serverClient
      .from('users')
      .insert({
        id: authData.user.id,
        email,
        name,
        role: 'ADMIN',
        is_active: true,
        is_onboarded: true,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      });

    if (dbError) {
      console.error('Database user creation failed:', dbError);
      
      // Clean up auth user if database insert fails
      await serverClient.auth.admin.deleteUser(authData.user.id);
      
      return NextResponse.json(
        { success: false, error: `Failed to create user profile: ${dbError.message}` },
        { status: 500 }
      );
    }

    console.log('Admin user created successfully:', { id: authData.user.id, email });

    return NextResponse.json({
      success: true,
      message: 'Admin account created successfully',
      user: {
        id: authData.user.id,
        email,
        name,
        role: 'ADMIN'
      }
    });

  } catch (error: any) {
    console.error('Admin registration error:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}
