'use client';

import { cn } from '@/lib/utils';
import { motion } from 'framer-motion';
import { ReactNode } from 'react';

interface MobileOptimizedContainerProps {
  children: ReactNode;
  className?: string;
  enableMotion?: boolean;
  motionProps?: any;
  spacing?: 'tight' | 'normal' | 'loose';
  maxWidth?: 'sm' | 'md' | 'lg' | 'xl' | '2xl' | 'full';
}

const spacingClasses = {
  tight: 'px-4 sm:px-6 md:px-8 lg:px-10',
  normal: 'px-4 sm:px-6 md:px-8 lg:px-12 xl:px-16',
  loose: 'px-6 sm:px-8 md:px-12 lg:px-16 xl:px-20'
};

// Mobile-specific spacing utilities
export const mobileSpacing = {
  // Vertical spacing
  section: 'py-12 sm:py-16 md:py-20 lg:py-24 xl:py-32',
  sectionSmall: 'py-8 sm:py-12 md:py-16 lg:py-20',
  sectionLarge: 'py-16 sm:py-20 md:py-24 lg:py-32 xl:py-40',

  // Component spacing
  component: 'mb-8 sm:mb-12 md:mb-16 lg:mb-20',
  componentSmall: 'mb-6 sm:mb-8 md:mb-10 lg:mb-12',
  componentLarge: 'mb-12 sm:mb-16 md:mb-20 lg:mb-24',

  // Element spacing
  element: 'mb-4 sm:mb-6 md:mb-8',
  elementSmall: 'mb-2 sm:mb-3 md:mb-4',
  elementLarge: 'mb-6 sm:mb-8 md:mb-10 lg:mb-12',

  // Grid gaps
  gridTight: 'gap-3 sm:gap-4 md:gap-6',
  gridNormal: 'gap-4 sm:gap-6 md:gap-8 lg:gap-10',
  gridLoose: 'gap-6 sm:gap-8 md:gap-10 lg:gap-12 xl:gap-16',

  // Touch targets (minimum 44px for accessibility)
  touchTarget: 'min-h-[44px] min-w-[44px]',
  touchTargetLarge: 'min-h-[56px] min-w-[56px]',

  // Safe areas for mobile
  safeTop: 'pt-safe-top',
  safeBottom: 'pb-safe-bottom',
  safeLeft: 'pl-safe-left',
  safeRight: 'pr-safe-right'
};

const maxWidthClasses = {
  sm: 'max-w-2xl',
  md: 'max-w-4xl',
  lg: 'max-w-6xl',
  xl: 'max-w-7xl',
  '2xl': 'max-w-8xl',
  full: 'max-w-full'
};

export function MobileOptimizedContainer({
  children,
  className,
  enableMotion = false,
  motionProps = {},
  spacing = 'normal',
  maxWidth = 'xl'
}: MobileOptimizedContainerProps) {
  const containerClasses = cn(
    'mx-auto w-full',
    spacingClasses[spacing],
    maxWidthClasses[maxWidth],
    className
  );

  if (enableMotion) {
    return (
      <motion.div
        className={containerClasses}
        {...motionProps}
      >
        {children}
      </motion.div>
    );
  }

  return (
    <div className={containerClasses}>
      {children}
    </div>
  );
}

// Mobile-optimized grid component
interface MobileGridProps {
  children: ReactNode;
  className?: string;
  cols?: {
    mobile: number;
    tablet: number;
    desktop: number;
  };
  gap?: 'sm' | 'md' | 'lg';
}

const gapClasses = {
  sm: 'gap-4',
  md: 'gap-6',
  lg: 'gap-8'
};

export function MobileGrid({
  children,
  className,
  cols = { mobile: 1, tablet: 2, desktop: 3 },
  gap = 'md'
}: MobileGridProps) {
  const gridClasses = cn(
    'grid',
    `grid-cols-${cols.mobile}`,
    `md:grid-cols-${cols.tablet}`,
    `lg:grid-cols-${cols.desktop}`,
    gapClasses[gap],
    className
  );

  return (
    <div className={gridClasses}>
      {children}
    </div>
  );
}

// Mobile-optimized text component
interface MobileTextProps {
  children: ReactNode;
  variant?: 'h1' | 'h2' | 'h3' | 'h4' | 'body' | 'caption';
  className?: string;
  responsive?: boolean;
}

const textVariants = {
  h1: {
    base: 'text-3xl font-bold leading-tight',
    responsive: 'text-2xl xs:text-3xl sm:text-4xl md:text-5xl lg:text-6xl xl:text-7xl font-bold leading-tight tracking-tight'
  },
  h2: {
    base: 'text-2xl font-bold leading-tight',
    responsive: 'text-xl xs:text-2xl sm:text-3xl md:text-4xl lg:text-5xl xl:text-6xl font-bold leading-tight tracking-tight'
  },
  h3: {
    base: 'text-xl font-semibold leading-tight',
    responsive: 'text-lg xs:text-xl sm:text-2xl md:text-3xl lg:text-4xl xl:text-5xl font-semibold leading-tight'
  },
  h4: {
    base: 'text-lg font-semibold leading-tight',
    responsive: 'text-base xs:text-lg sm:text-xl md:text-2xl lg:text-3xl xl:text-4xl font-semibold leading-tight'
  },
  body: {
    base: 'text-base leading-relaxed',
    responsive: 'text-sm xs:text-base sm:text-base md:text-lg lg:text-xl leading-relaxed'
  },
  caption: {
    base: 'text-sm leading-normal',
    responsive: 'text-xs xs:text-sm sm:text-sm md:text-base leading-normal'
  }
};

export function MobileText({
  children,
  variant = 'body',
  className,
  responsive = true
}: MobileTextProps) {
  const textClasses = cn(
    responsive ? textVariants[variant].responsive : textVariants[variant].base,
    className
  );

  const Component = variant.startsWith('h') ? variant : 'p';

  return (
    <Component className={textClasses}>
      {children}
    </Component>
  );
}

// Mobile-optimized button component
interface MobileButtonProps {
  children: ReactNode;
  className?: string;
  size?: 'sm' | 'md' | 'lg';
  variant?: 'primary' | 'secondary' | 'outline';
  fullWidth?: boolean;
  onClick?: () => void;
  disabled?: boolean;
  type?: 'button' | 'submit' | 'reset';
}

const buttonSizes = {
  sm: 'px-4 py-2 text-sm min-h-[40px]',
  md: 'px-6 py-3 text-base min-h-[48px]',
  lg: 'px-8 py-4 text-lg min-h-[56px]'
};

const buttonVariants = {
  primary: 'bg-purple-600 hover:bg-purple-700 text-white border border-purple-600',
  secondary: 'bg-white/10 hover:bg-white/20 text-white border border-white/20',
  outline: 'bg-transparent hover:bg-purple-600/10 text-purple-400 border border-purple-500'
};

export function MobileButton({
  children,
  className,
  size = 'md',
  variant = 'primary',
  fullWidth = false,
  onClick,
  disabled = false,
  type = 'button'
}: MobileButtonProps) {
  return (
    <motion.button
      type={type}
      onClick={onClick}
      disabled={disabled}
      className={cn(
        'rounded-xl font-medium transition-all duration-300 active:scale-95 disabled:opacity-50 disabled:cursor-not-allowed',
        buttonSizes[size],
        buttonVariants[variant],
        fullWidth && 'w-full',
        className
      )}
      whileTap={{ scale: disabled ? 1 : 0.95 }}
      whileHover={{ scale: disabled ? 1 : 1.02 }}
      transition={{ duration: 0.2 }}
    >
      {children}
    </motion.button>
  );
}

// Mobile-optimized card component
interface MobileCardProps {
  children: ReactNode;
  className?: string;
  padding?: 'sm' | 'md' | 'lg';
  hover?: boolean;
}

const cardPadding = {
  sm: 'p-4',
  md: 'p-6',
  lg: 'p-8'
};

export function MobileCard({
  children,
  className,
  padding = 'md',
  hover = true
}: MobileCardProps) {
  return (
    <motion.div
      className={cn(
        'bg-black/40 backdrop-blur-md border border-white/10 rounded-xl',
        cardPadding[padding],
        hover && 'hover:border-purple-500/30 hover:bg-black/60',
        'transition-all duration-300',
        className
      )}
      whileHover={hover ? { y: -4 } : undefined}
      transition={{ duration: 0.3 }}
    >
      {children}
    </motion.div>
  );
}
